{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "ijGzTHJJUCPY"}, "outputs": [], "source": ["# Copyright 2023 Google LLC\n", "#\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#\n", "#     https://www.apache.org/licenses/LICENSE-2.0\n", "#\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "metadata": {"id": "VEqbX8OhE8y9"}, "source": ["# Gemini: An Overview of Multimodal Use Cases\n", "\n", "<table align=\"left\">\n", "  <td style=\"text-align: center\">\n", "    <a href=\"https://colab.research.google.com/github/GoogleCloudPlatform/generative-ai/blob/main/gemini/use-cases/intro_multimodal_use_cases.ipynb\">\n", "      <img src=\"https://cloud.google.com/ml-engine/images/colab-logo-32px.png\" alt=\"Google Colaboratory logo\"><br> Run in Colab\n", "    </a>\n", "  </td>\n", "  <td style=\"text-align: center\">\n", "    <a href=\"https://console.cloud.google.com/vertex-ai/colab/import/https:%2F%2Fraw.githubusercontent.com%2FGoogleCloudPlatform%2Fgenerative-ai%2Fmain%2Fgemini%2Fuse-cases%2Fintro_multimodal_use_cases.ipynb\">\n", "      <img width=\"32px\" src=\"https://lh3.googleusercontent.com/JmcxdQi-qOpctIvWKgPtrzZdJJK-J3sWE1RsfjZNwshCFgE_9fULcNpuXYTilIR2hjwN\" alt=\"Google Cloud Colab Enterprise logo\"><br> Run in Colab Enterprise\n", "    </a>\n", "  </td>\n", "  <td style=\"text-align: center\">\n", "    <a href=\"https://console.cloud.google.com/vertex-ai/workbench/deploy-notebook?download_url=https://raw.githubusercontent.com/GoogleCloudPlatform/generative-ai/main/gemini/use-cases/intro_multimodal_use_cases.ipynb\">\n", "      <img src=\"https://lh3.googleusercontent.com/UiNooY4LUgW_oTvpsNhPpQzsstV5W8F7rYgxgGBD85cWJoLmrOzhVs_ksK_vgx40SHs7jCqkTkCk=e14-rj-sc0xffffff-h130-w32\" alt=\"Vertex AI logo\"><br> Open in Vertex AI Workbench\n", "    </a>\n", "  </td>\n", "  <td style=\"text-align: center\">\n", "    <a href=\"https://github.com/GoogleCloudPlatform/generative-ai/blob/main/gemini/use-cases/intro_multimodal_use_cases.ipynb\">\n", "      <img src=\"https://cloud.google.com/ml-engine/images/github-logo-32px.png\" alt=\"GitHub logo\"><br> View on GitHub\n", "    </a>\n", "  </td>\n", "  <td style=\"text-align: center\">\n", "    <a href=\"https://goo.gle/3DUssjz\">\n", "      <img width=\"32px\" src=\"https://cdn.qwiklabs.com/assets/gcp_cloud-e3a77215f0b8bfa9b3f611c0d2208c7e8708ed31.svg\" alt=\"Google Cloud logo\"><br> Open in  Cloud Skills Boost\n", "    </a>\n", "  </td>\n", "</table>\n", "\n", "<div style=\"clear: both;\"></div>\n", "\n", "<b>Share to:</b>\n", "\n", "<a href=\"https://www.linkedin.com/sharing/share-offsite/?url=https%3A//github.com/GoogleCloudPlatform/generative-ai/blob/main/gemini/use-cases/intro_multimodal_use_cases.ipynb\" target=\"_blank\">\n", "  <img width=\"20px\" src=\"https://upload.wikimedia.org/wikipedia/commons/8/81/LinkedIn_icon.svg\" alt=\"LinkedIn logo\">\n", "</a>\n", "\n", "<a href=\"https://bsky.app/intent/compose?text=https%3A//github.com/GoogleCloudPlatform/generative-ai/blob/main/gemini/use-cases/intro_multimodal_use_cases.ipynb\" target=\"_blank\">\n", "  <img width=\"20px\" src=\"https://upload.wikimedia.org/wikipedia/commons/7/7a/Bluesky_Logo.svg\" alt=\"<PERSON><PERSON> logo\">\n", "</a>\n", "\n", "<a href=\"https://twitter.com/intent/tweet?url=https%3A//github.com/GoogleCloudPlatform/generative-ai/blob/main/gemini/use-cases/intro_multimodal_use_cases.ipynb\" target=\"_blank\">\n", "  <img width=\"20px\" src=\"https://upload.wikimedia.org/wikipedia/commons/5/5a/X_icon_2.svg\" alt=\"X logo\">\n", "</a>\n", "\n", "<a href=\"https://reddit.com/submit?url=https%3A//github.com/GoogleCloudPlatform/generative-ai/blob/main/gemini/use-cases/intro_multimodal_use_cases.ipynb\" target=\"_blank\">\n", "  <img width=\"20px\" src=\"https://redditinc.com/hubfs/Reddit%20Inc/Brand/Reddit_Logo.png\" alt=\"Reddit logo\">\n", "</a>\n", "\n", "<a href=\"https://www.facebook.com/sharer/sharer.php?u=https%3A//github.com/GoogleCloudPlatform/generative-ai/blob/main/gemini/use-cases/intro_multimodal_use_cases.ipynb\" target=\"_blank\">\n", "  <img width=\"20px\" src=\"https://upload.wikimedia.org/wikipedia/commons/5/51/Facebook_f_logo_%282019%29.svg\" alt=\"Facebook logo\">\n", "</a>            \n"]}, {"cell_type": "markdown", "metadata": {"id": "8HKLOuOlJutv"}, "source": ["| Authors |\n", "| --- |\n", "| [<PERSON>](https://github.com/katiemn) |\n", "| [<PERSON><PERSON>](https://github.com/saeed<PERSON>ab<PERSON>orgi) |"]}, {"cell_type": "markdown", "metadata": {"id": "VK1Q5ZYdVL4Y"}, "source": ["## Overview\n", "\n", "**YouTube Video: Multimodal AI in action**\n", "\n", "<a href=\"https://www.youtube.com/watch?v=pEmCgIGpIoo&list=PLIivdWyY5sqJio2yeg1dlfILOUO2FoFRx\" target=\"_blank\">\n", "  <img src=\"https://img.youtube.com/vi/pEmCgIGpIoo/maxresdefault.jpg\" alt=\"Multimodal AI in action\" width=\"500\">\n", "</a>\n", "\n", "In this notebook, you will explore a variety of different use cases enabled by multimodality with Gemini.\n", "\n", "Gemini is a family of generative AI models developed by [Google DeepMind](https://deepmind.google/) that is designed for multimodal use cases. [Gemini 2.0](https://cloud.google.com/vertex-ai/generative-ai/docs/gemini-v2) is the latest model version.\n", "\n", "### Gemini 2.0 Flash\n", "\n", "This smaller Gemini model is optimized for high-frequency tasks to prioritize the model's response time. This model has superior speed and efficiency with a context window of up to 1 million tokens for all modalities.\n", "\n", "For more information, see the [Generative AI on Vertex AI](https://cloud.google.com/vertex-ai/docs/generative-ai/learn/overview) documentation.\n", "\n", "### Objectives\n", "\n", "This notebook demonstrates a variety of multimodal use cases with Gemini.\n", "\n", "In this tutorial, you will learn how to use Gemini with the Gen AI SDK for Python to:\n", "\n", "  - Process and generate text\n", "  - Parse and summarize PDF documents\n", "  - Reason across multiple images\n", "  - Generating a video description\n", "  - Combining video data with external knowledge\n", "  - Understand Audio\n", "  - Analyze a code base\n", "  - Combine modalities\n", "  - Recommendation based on user preferences for e-commerce\n", "  - Understanding charts and diagrams\n", "  - Comparing images for similarities, anomalies, or differences"]}, {"cell_type": "markdown", "metadata": {"id": "ZhsUe0fyc-ER"}, "source": ["### Costs\n", "\n", "This tutorial uses billable components of Google Cloud:\n", "\n", "- Vertex AI\n", "\n", "Learn about [Vertex AI pricing](https://cloud.google.com/vertex-ai/pricing) and use the [Pricing Calculator](https://cloud.google.com/products/calculator/) to generate a cost estimate based on your projected usage.\n"]}, {"cell_type": "markdown", "metadata": {"id": "QDU0XJ1xRDlL"}, "source": ["## Getting Started\n"]}, {"cell_type": "markdown", "metadata": {"id": "N5afkyDMSBW5"}, "source": ["### Install Google Gen AI SDK for Python"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "kc4WxYmLSBW5", "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install --upgrade --quiet google-genai gitingest"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Restart runtime\n", "\n", "To use the newly installed packages in this Jupyter runtime, you must restart the runtime. You can do this by running the cell below, which restarts the current kernel."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["{'status': 'ok', 'restart': True}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import IPython\n", "\n", "app = IPython.Application.instance()\n", "app.kernel.do_shutdown(True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"alert alert-block alert-warning\">\n", "<b>⚠️ The kernel is going to restart. The restart might take a minute or longer. After it's restarted, continue to the next step. ⚠️</b>\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {"id": "6Fom0ZkMSBW6"}, "source": ["### Authenticate your notebook environment (Colab only)\n", "\n", "If you are running this notebook on Google Colab, run the following cell to authenticate your environment. This step is not required if you are using [Vertex AI Workbench](https://cloud.google.com/vertex-ai-workbench).\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "LCaCx6PLSBW6"}, "outputs": [], "source": ["import sys\n", "\n", "# Additional authentication is required for Google Colab\n", "if \"google.colab\" in sys.modules:\n", "    # Authenticate user to Google Cloud\n", "    from google.colab import auth\n", "\n", "    auth.authenticate_user()"]}, {"cell_type": "markdown", "metadata": {"id": "QGB8Txa_e4V0"}, "source": ["### Set Google Cloud project information and create client\n", "\n", "To get started using Vertex AI, you must have an existing Google Cloud project and [enable the Vertex AI API](https://console.cloud.google.com/flows/enableapi?apiid=aiplatform.googleapis.com).\n", "\n", "Learn more about [setting up a project and a development environment](https://cloud.google.com/vertex-ai/docs/start/cloud-environment)."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "JGOJHtgDe5-r", "tags": []}, "outputs": [], "source": ["from google import genai\n", "\n", "PROJECT_ID = \"qwiklabs-gcp-02-b611d3b7b253\"  # @param {type:\"string\"}\n", "LOCATION = \"us-east4\"  # @param {type:\"string\"}\n", "\n", "client = genai.Client(vertexai=True, project=PROJECT_ID, location=LOCATION)"]}, {"cell_type": "markdown", "metadata": {"id": "BuQwwRiniVFG"}, "source": ["### Import libraries\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "JTk488WDPBtQ", "tags": []}, "outputs": [], "source": ["from IPython.display import Audio, Image, Markdown, Video, display\n", "from gitingest import ingest\n", "from google.genai.types import CreateCachedContentConfig, GenerateContentConfig, Part\n", "import nest_asyncio\n", "\n", "nest_asyncio.apply()"]}, {"cell_type": "markdown", "metadata": {"id": "eTNnM-lqfQRo"}, "source": ["### Load Gemini 2.0 Flash model\n", "\n", "Learn more about all [Gemini models on Vertex AI](https://cloud.google.com/vertex-ai/generative-ai/docs/learn/models#gemini-models)."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "2998506fe6d1", "tags": []}, "outputs": [], "source": ["MODEL_ID = \"gemini-2.0-flash-001\"  # @param {type: \"string\"}"]}, {"cell_type": "markdown", "metadata": {"id": "22c7363baeb0"}, "source": ["## Individual Modalities"]}, {"cell_type": "markdown", "metadata": {"id": "a052fcef47ea"}, "source": ["### Textual understanding\n", "\n", "Gemini can parse textual questions and retain that context across following prompts."]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "577574234d7e", "tags": []}, "outputs": [{"data": {"text/markdown": ["Okay, let's break down the average weather in Himachal Pradesh in mid-December and then dive into food and celebrity recommendations.\n", "\n", "**Average Weather in Himachal Pradesh in Mid-December:**\n", "\n", "Himachal Pradesh in mid-December is characterized by cold and often snowy conditions, especially in higher altitudes. Here's a general overview:\n", "\n", "*   **Temperature:**\n", "    *   **Lower Hills (e.g., Shimla, Dharamshala):**  Daytime temperatures typically range from 5°C to 12°C (41°F to 54°F). Nighttime temperatures can drop to freezing or even below, ranging from -2°C to 5°C (28°F to 41°F).\n", "    *   **Higher Hills (e.g., Manali, Kufri):** Temperatures are significantly colder. Daytime highs might be around 0°C to 7°C (32°F to 45°F), and nighttime lows can plummet to -5°C to -12°C (23°F to 10°F) or even lower, especially with snowfall.\n", "*   **Snowfall:** Mid-December is a good time to experience snowfall, especially in higher altitude regions like Manali, Kufri, Narkanda, and areas around Rohtang Pass (if accessible). Shimla also often receives snowfall around this time.\n", "*   **Overall:** Expect cold, dry weather. Layering clothing is essential. Carry warm jackets, sweaters, thermals, gloves, hats, and scarves. Ensure you have proper footwear suitable for walking in snow or icy conditions, especially if you're planning to visit areas known for snowfall.\n", "\n", "**Food Suggestions for Cold Weather in Himachal Pradesh:**\n", "\n", "Given the chilly weather, hearty, warming, and locally-sourced foods are ideal.\n", "\n", "*   **Thukpa:** A Tibetan noodle soup with meat (usually chicken or mutton) or vegetables. It's flavorful, filling, and excellent for warming up.\n", "*   **Momos:** Steamed or fried dumplings filled with meat or vegetables. Served with spicy chutney, they are a popular street food and comfort food option.\n", "*   **Siddu:** A steamed bread made from wheat flour and stuffed with ingredients like poppy seeds, walnuts, or potatoes. It's a traditional Himachali dish, often served with ghee or dal.\n", "*   **Madra:** A creamy yogurt-based curry made with chickpeas or kidney beans. It's rich, flavorful, and often enjoyed with rice or roti. This is a Chamba dish.\n", "*   **Dham:** A traditional festive thali (platter) typically served during special occasions. It includes dishes like rice, dal, rajma, madra, khatta (a sour and tangy dish), and sweet rice (meethe chawal). Every region has its version.\n", "*   **Trout Fish:** Himachal Pradesh is known for its trout farms. Freshly cooked trout, either grilled, fried, or in a curry, is a delicious and healthy option.\n", "*   **Kangra Tea:** A local tea variety known for its distinctive flavor and aroma. It's perfect for sipping on a cold day.\n", "*   **<PERSON><PERSON><PERSON> (Morel) Mushroom Dishes:** Gucchi mushrooms are a delicacy in Himachal Pradesh. They are expensive but have a unique earthy flavor. Try them in pulao, curries, or stir-fries.\n", "*   **Soups:** Vegetable soup, mutton soup, or chicken soup.\n", "*   **Ginger and Honey Tea or Hot Lemon Water:** Soothe the throat and provide warmth.\n", "*   **<PERSON><PERSON><PERSON> with <PERSON><PERSON>:** The hot sweetness will be very satisfying.\n", "\n", "**Celebrity Recommendations & Anecdotes (More Hypothetical/Inspired):**\n", "\n", "It's difficult to find specific, documented food recommendations from celebrities *in Himachal Pradesh*. Celebrity travel to the region is relatively private. However, we can imagine/extrapolate based on their known preferences and the region's offerings:\n", "\n", "*   **<PERSON><PERSON><PERSON>:** Known for her love of Indian cuisine, she might appreciate a good Dham experience or a flavorful Madra dish. She might also be interested in trying the local Kangra tea.\n", "*   **<PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>:** Given their focus on health and fitness, they might be drawn to the fresh trout fish and the regional dishes made with local ingredients and less oil.\n", "*   **<PERSON><PERSON>:** Being a South Indian by origin, she might be drawn to comfort food like rice and lentil-based dishes which are common in Dham. Also, I can imagine her relishing the simplicity of hot, freshly made Siddu.\n", "\n", "**Disclaimer:** I do not have access to real-time celebrity travel logs or dietary preferences. This information is extrapolated based on common knowledge and assumptions.\n"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["question = \"What is the average weather in himachal pradesh , india in the middle of december?\"\n", "prompt = \"\"\"\n", "Considering the weather, please provide some food suggestions.\n", "\n", "Give example of some celebreties recommendations.\n", "\"\"\"\n", "\n", "contents = [question, prompt]\n", "response = client.models.generate_content(model=MODEL_ID, contents=contents)\n", "display(Markdown(response.text))"]}, {"cell_type": "markdown", "metadata": {"id": "7d5586dd92ed"}, "source": ["### Document Summarization\n", "\n", "You can use Gemini to process PDF documents, and analyze content, retain information, and provide answers to queries regarding the documents.\n", "\n", "The PDF document example used here is the Gemini 2.0 paper (https://arxiv.org/pdf/2403.05530.pdf).\n", "\n", "![image.png](https://storage.googleapis.com/cloud-samples-data/generative-ai/image/gemini1.5-paper-2403.05530.png)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"id": "d5af46d4da0c", "tags": []}, "outputs": [{"data": {"text/markdown": ["The Gemini 1.5 Pro model can process up to at least 10M tokens."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pdf_file_uri = \"gs://cloud-samples-data/generative-ai/pdf/2403.05530.pdf\"\n", "pdf_file = Part.from_uri(file_uri=pdf_file_uri, mime_type=\"application/pdf\")\n", "\n", "prompt = \"How many tokens can the model process?\"\n", "\n", "contents = [pdf_file, prompt]\n", "\n", "response = client.models.generate_content(model=MODEL_ID, contents=contents)\n", "display(Markdown(response.text))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"id": "25658ef8dcec", "tags": []}, "outputs": [{"data": {"text/markdown": ["Here is a summary of the document:\n", "\n", "This report introduces Gemini 1.5 Pro, a highly compute-efficient multimodal mixture-of-experts model capable of recalling and reasoning over fine-grained information from millions of tokens of context, including multiple long documents and hours of video and audio. Gemini 1.5 Pro achieves near-perfect recall on long-context retrieval tasks across modalities, improves the state-of-the-art in long-document QA, long-video QA and long-context ASR, and matches or surpasses Gemini 1.0 Ultra's state-of-the-art performance across a broad set of benchmarks. The model demonstrates continued improvement in next-token prediction and near-perfect retrieval (>99%) up to at least 10M tokens. It also exhibits surprising new capabilities, such as learning to translate English to Kalamang, a language with fewer than 200 speakers worldwide, at a level similar to a person who learned from the same content. The report details the model architecture, quantitative evaluations, long-context capabilities, core capabilities, and approach to responsible deployment. It also includes a model card and appendices with further information."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["prompt = \"\"\"\n", "  You are a professional document summarization specialist.\n", "  Please summarize the given document.\n", "\"\"\"\n", "\n", "contents = [pdf_file, prompt]\n", "\n", "response = client.models.generate_content(model=MODEL_ID, contents=contents)\n", "display(Markdown(response.text))"]}, {"cell_type": "markdown", "metadata": {"id": "5OWurhO4mu4J"}, "source": ["### Image understanding across multiple images\n", "\n", "One of the capabilities of Gemini is being able to reason across multiple images.\n", "\n", "This is an example of using Gemini to reason which glasses would be more suitable for an oval face shape."]}, {"cell_type": "code", "execution_count": 15, "metadata": {"id": "80048d6d0123", "tags": []}, "outputs": [{"data": {"image/jpeg": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {"image/jpeg": {"width": 150}}, "output_type": "display_data"}, {"data": {"image/jpeg": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {"image/jpeg": {"width": 150}}, "output_type": "display_data"}, {"data": {"text/markdown": ["Okay, let's determine which of those glasses would be more flattering for an oval face shape.\n", "\n", "**Understanding <PERSON>pes**\n", "\n", "Oval faces are considered the most versatile shape for eyeglasses. They have balanced proportions: the forehead and jawline are relatively narrow, and the face is longer than it is wide. This even distribution allows oval faces to wear a wide variety of frame styles. The goal is to maintain this balance and not disrupt the natural symmetry.\n", "\n", "**Analysis of the Glasses**\n", "\n", "*   **Image 1: Rectangular Glasses:** These glasses have a sharp, geometric shape. The frame appears to be a lighter material, such as metal.\n", "*   **Image 2: Round Glasses:** These glasses feature a circular lens shape, and the frame appears to be made of a thicker material, such as plastic.\n", "\n", "**Recommendation and Justification**\n", "\n", "Considering that you have an oval face shape, *both* styles can work well! Here's a breakdown:\n", "\n", "*   **Round Glasses: A Strong Choice** Round glasses provide a good contrast to the oval face's natural curves. The circular shape adds softness and can help to make a longer face appear a bit wider. The thicker frame can also add a touch of personality and style.\n", "\n", "*   **Rectangular Glasses: A Solid Option** Rectangular frames can add some definition to an oval face. The straight lines can provide a more angular contrast. However, be mindful of the size and sharpness. Avoid frames that are too wide or too angular, as this could make your face appear longer or too harsh. A softer rectangle or a rectangular frame with rounded edges would be an excellent choice.\n", "\n", "**Additional Considerations**\n", "\n", "*   **Size:** Regardless of the shape, make sure the glasses are appropriately sized for your face. The frames should not extend beyond the widest part of your face, and your eyes should be centered within the lenses.\n", "\n", "*   **Personal Style:** Ultimately, the best glasses for you will depend on your personal preferences and style. If you prefer a bolder look, the round glasses might be a better choice. If you prefer something more classic and understated, the rectangular glasses could be a good fit.\n", "\n", "*   **Material and Color:** Consider the material and color of the frames as well. Thicker frames will make a stronger statement, while thinner frames will be more subtle. The color of the frames should complement your skin tone and hair color.\n", "\n", "In conclusion, I recommend the round glasses or rectangular glasses with softened edges as they generally complement the oval face.\n"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["image_glasses1_url = \"https://storage.googleapis.com/github-repo/img/gemini/multimodality_usecases_overview/glasses1.jpg\"\n", "image_glasses2_url = \"https://storage.googleapis.com/github-repo/img/gemini/multimodality_usecases_overview/glasses2.jpg\"\n", "\n", "display(Image(image_glasses1_url, width=150))\n", "display(Image(image_glasses2_url, width=150))\n", "\n", "prompt = \"\"\"\n", "I have an oval face. Given my face shape, which glasses would be more suitable?\n", "\n", "Explain how you reached this decision.\n", "Provide your recommendation based on my face shape, and please give an explanation for each.\n", "\"\"\"\n", "\n", "contents = [\n", "    prompt,\n", "    Part.from_uri(file_uri=image_glasses1_url, mime_type=\"image/jpeg\"),\n", "    Part.from_uri(file_uri=image_glasses2_url, mime_type=\"image/jpeg\"),\n", "]\n", "response = client.models.generate_content(model=MODEL_ID, contents=contents)\n", "display(Markdown(response.text))"]}, {"cell_type": "markdown", "metadata": {"id": "96b21923035e"}, "source": ["### Generating a video description\n", "\n", "Gemini can also extract tags throughout a video:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "3cef00d36cde"}, "outputs": [], "source": ["video_url = \"https://storage.googleapis.com/github-repo/img/gemini/multimodality_usecases_overview/mediterraneansea.mp4\"\n", "display(Video(video_url, width=350))\n", "\n", "prompt = \"\"\"\n", "What is shown in this video?\n", "Where should I go to see it?\n", "What are the top 5 places in the world that look like this?\n", "Provide the 10 best tags for this video?\n", "\"\"\"\n", "\n", "video = Part.from_uri(\n", "    file_uri=video_url,\n", "    mime_type=\"video/mp4\",\n", ")\n", "contents = [prompt, video]\n", "\n", "response = client.models.generate_content(model=MODEL_ID, contents=contents)\n", "display(Markdown(response.text))"]}, {"cell_type": "markdown", "metadata": {"id": "ca8100e88501"}, "source": ["> You can confirm that the location is indeed Antalya, Turkey by visiting the Wikipedia page: https://en.wikipedia.org/wiki/Antalya"]}, {"cell_type": "markdown", "metadata": {"id": "2547a8887702"}, "source": ["You can also use Gemini to retrieve extra information beyond the video contents."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "c978cf9f8c71"}, "outputs": [], "source": ["video_url = \"https://storage.googleapis.com/github-repo/img/gemini/multimodality_usecases_overview/ottawatrain3.mp4\"\n", "display(Video(video_url, width=350))\n", "\n", "prompt = \"\"\"\n", "Which train line is this?\n", "Where does it go?\n", "What are the stations/stops?\n", "Which river is being crossed?\n", "\"\"\"\n", "\n", "video = Part.from_uri(\n", "    file_uri=video_url,\n", "    mime_type=\"video/mp4\",\n", ")\n", "contents = [prompt, video]\n", "\n", "response = client.models.generate_content(\n", "    model=MODEL_ID, contents=contents, config=GenerateContentConfig(temperature=0)\n", ")\n", "display(Markdown(response.text))"]}, {"cell_type": "markdown", "metadata": {"id": "8a614b7f5284"}, "source": ["> You can confirm that this is indeed the Confederation Line on Wikipedia here: https://en.wikipedia.org/wiki/Confederation_Line"]}, {"cell_type": "markdown", "metadata": {"id": "8fb9a30ff4c5"}, "source": ["### Audio understanding\n", "\n", "Gemini can directly process audio for long-context understanding."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "5424dbe4c7e1"}, "outputs": [], "source": ["audio_url = (\n", "    \"https://storage.googleapis.com/cloud-samples-data/generative-ai/audio/pixel.mp3\"\n", ")\n", "display(Audio(audio_url))"]}, {"cell_type": "markdown", "metadata": {"id": "9ed4ea9d696f"}, "source": ["#### Summarization"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "c889e8db2aca"}, "outputs": [], "source": ["prompt = \"\"\"\n", "  Please provide a short summary and title for the audio.\n", "  Provide chapter titles, be concise and short, no need to provide chapter summaries.\n", "  Provide each of the chapter titles in a numbered list.\n", "  Do not make up any information that is not part of the audio and do not be verbose.\n", "\"\"\"\n", "\n", "audio_file = Part.from_uri(file_uri=audio_url, mime_type=\"audio/mpeg\")\n", "contents = [audio_file, prompt]\n", "\n", "response = client.models.generate_content(model=MODEL_ID, contents=contents)\n", "display(Markdown(response.text))"]}, {"cell_type": "markdown", "metadata": {"id": "e64eb3061613"}, "source": ["#### Transcription"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "4486a25573a0"}, "outputs": [], "source": ["prompt = \"\"\"\n", "    Transcribe this interview, in the format of timecode, speaker, caption.\n", "    Use speaker A, speaker B, etc. to identify the speakers.\n", "    Provide each piece of information on a separate bullet point.\n", "\"\"\"\n", "\n", "audio_file = Part.from_uri(file_uri=audio_url, mime_type=\"audio/mpeg\")\n", "contents = [audio_file, prompt]\n", "\n", "response = client.models.generate_content(\n", "    model=MODEL_ID,\n", "    contents=contents,\n", "    config=GenerateContentConfig(max_output_tokens=8192),\n", ")\n", "display(Markdown(response.text))"]}, {"cell_type": "markdown", "metadata": {"id": "96c026a7f2ce"}, "source": ["### Reason across a codebase\n", "\n", "You will use the [Online Boutique repository](https://github.com/GoogleCloudPlatform/microservices-demo) as an example in this notebook. Online Boutique is a cloud-first microservices demo application. The application is a web-based e-commerce app where users can browse items, add them to the cart, and purchase them. This application consists of 11 microservices across multiple languages."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ad22725252dc"}, "outputs": [], "source": ["# The GitHub repository URL\n", "repo_url = \"https://github.com/GoogleCloudPlatform/microservices-demo\"  # @param {type:\"string\"}"]}, {"cell_type": "markdown", "metadata": {"id": "1acc008e3d83"}, "source": ["#### Create an index and extract the contents of a codebase\n", "\n", "Clone the repo and create an index and extract content of code/text files."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "7f7e3cce09c7"}, "outputs": [], "source": ["exclude_patterns = {\n", "    \"*.png\",\n", "    \"*.jpg\",\n", "    \"*.jpeg\",\n", "    \"*.gif\",\n", "    \"*.svg\",\n", "    \"*.ico\",\n", "    \"*.webp\",\n", "    \"*.jar\",\n", "    \".git/\",\n", "    \"*.gitkeep\",\n", "}\n", "_, code_index, code_text = ingest(repo_url, exclude_patterns=exclude_patterns)"]}, {"cell_type": "markdown", "metadata": {"id": "9572811ab5b2"}, "source": ["#### Create a content cache for the codebase\n", "\n", "The codebase prompt is going to be quite large with all of the included data.\n", "Gemini supports [Context caching](https://cloud.google.com/vertex-ai/generative-ai/docs/context-cache/context-cache-overview), which lets you to store frequently used input tokens in a dedicated cache and reference them for subsequent requests, eliminating the need to repeatedly pass the same set of tokens to a model.\n", "\n", "**Note**: Context caching is only available for stable models with fixed versions (for example, `gemini-2.0-flash-001`). You must include the version postfix (for example, the `-001`)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "daeff13cca4a"}, "outputs": [], "source": ["prompt = f\"\"\"\n", "Context:\n", "- The entire codebase is provided below.\n", "- Here is an index of all of the files in the codebase:\n", "    \\n\\n{code_index}\\n\\n.\n", "- Then each of the files is concatenated together. You will find all of the code you need:\n", "    \\n\\n{code_text}\\n\\n\n", "\"\"\"\n", "\n", "cached_content = client.caches.create(\n", "    model=\"gemini-2.0-flash-001\",\n", "    config=CreateCachedContentConfig(\n", "        contents=prompt,\n", "        ttl=\"3600s\",\n", "    ),\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "3d519f3b1763"}, "source": ["#### Create a developer getting started guide"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "fad43c9b32de"}, "outputs": [], "source": ["question = \"\"\"\n", "  Provide a getting started guide to onboard new developers to the codebase.\n", "\"\"\"\n", "\n", "response = client.models.generate_content(\n", "    model=\"gemini-2.0-flash-001\",\n", "    contents=question,\n", "    config=GenerateContentConfig(\n", "        cached_content=cached_content.name,\n", "    ),\n", ")\n", "display(Markdown(response.text))"]}, {"cell_type": "markdown", "metadata": {"id": "442e5eedc6dc"}, "source": ["#### Finding bugs in the code"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "95e1def33199"}, "outputs": [], "source": ["question = \"\"\"\n", "    Find the top 3 most severe issues in the codebase.\n", "\"\"\"\n", "\n", "response = client.models.generate_content(\n", "    model=\"gemini-2.0-flash-001\",\n", "    contents=question,\n", "    config=GenerateContentConfig(\n", "        cached_content=cached_content.name,\n", "    ),\n", ")\n", "display(Markdown(response.text))"]}, {"cell_type": "markdown", "metadata": {"id": "ae2123dfb85d"}, "source": ["#### Summarizing the codebase"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "807ab5b69ea8"}, "outputs": [], "source": ["question = \"\"\"\n", "  Give me a summary of this codebase, and tell me the top 3 things that I can learn from it.\n", "\"\"\"\n", "\n", "response = client.models.generate_content(\n", "    model=\"gemini-2.0-flash-001\",\n", "    contents=question,\n", "    config=GenerateContentConfig(\n", "        cached_content=cached_content.name,\n", "    ),\n", ")\n", "display(Markdown(response.text))"]}, {"cell_type": "markdown", "metadata": {"id": "06354c53e1a8"}, "source": ["## Combining multiple modalities"]}, {"cell_type": "markdown", "metadata": {"id": "3c00e534189b"}, "source": ["### Video and audio understanding\n", "\n", "Try out Gemini's native multimodal and long-context capabilities on video interleaving with audio inputs."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "41b5bb1b04c2"}, "outputs": [], "source": ["video_url = (\n", "    \"https://storage.googleapis.com/cloud-samples-data/generative-ai/video/pixel8.mp4\"\n", ")\n", "display(Video(video_url, width=350))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "a29e43974ca9"}, "outputs": [], "source": ["prompt = \"\"\"\n", "  Provide a detailed description of the video.\n", "  The description should also contain any important dialogue from the video and key features of the phone.\n", "\"\"\"\n", "\n", "video = Part.from_uri(\n", "    file_uri=video_url,\n", "    mime_type=\"video/mp4\",\n", ")\n", "contents = [prompt, video]\n", "\n", "response = client.models.generate_content(model=MODEL_ID, contents=contents)\n", "display(Markdown(response.text))"]}, {"cell_type": "markdown", "metadata": {"id": "8b8eec259f62"}, "source": ["### All modalities (images, video, audio, text) at once\n", "\n", "Gemini is natively multimodal and supports interleaving of data from different modalities. It can support a mix of audio, visual, text, and code inputs in the same input sequence."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ff9b20835ada"}, "outputs": [], "source": ["video_url = \"gs://cloud-samples-data/generative-ai/video/behind_the_scenes_pixel.mp4\"\n", "display(Video(video_url.replace(\"gs://\", \"https://storage.googleapis.com/\"), width=350))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ecec6597c63e"}, "outputs": [], "source": ["image_url = \"https://storage.googleapis.com/cloud-samples-data/generative-ai/image/a-man-and-a-dog.png\"\n", "display(Image(image_url, width=350))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "a3f2ae2fb517"}, "outputs": [], "source": ["prompt = \"\"\"\n", "  Look through each frame in the video carefully and answer the questions.\n", "  Only base your answers strictly on what information is available in the video attached.\n", "  Do not make up any information that is not part of the video and do not be too\n", "  verbose, be straightforward.\n", "\n", "  Questions:\n", "  - When is the moment in the image happening in the video? Provide a timestamp.\n", "  - What is the context of the moment and what does the narrator say about it?\n", "\"\"\"\n", "\n", "contents = [\n", "    prompt,\n", "    Part.from_uri(file_uri=video_url, mime_type=\"video/mp4\"),\n", "    Part.from_uri(file_uri=image_url, mime_type=\"image/png\"),\n", "]\n", "\n", "response = client.models.generate_content(model=MODEL_ID, contents=contents)\n", "display(Markdown(response.text))"]}, {"cell_type": "markdown", "metadata": {"id": "c4de7efa9637"}, "source": ["## Use Case: retail / e-commerce\n", "\n", "Suppose a customer shows you their living room and wants to find appropriate furniture and choose between four wall art options for the room.\n", "\n", "How can you use Gemini to help the customer choose the best option?"]}, {"cell_type": "markdown", "metadata": {"id": "aaaac1880330"}, "source": ["### Generating open recommendations\n", "\n", "Using the same image, you can ask the model to recommend a piece of furniture that would make sense in the space.\n", "\n", "Note that the model can choose any furniture in this case, and can do so only from its built-in knowledge."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "60ca92c5776c"}, "outputs": [], "source": ["room_image_url = \"https://storage.googleapis.com/cloud-samples-data/generative-ai/image/living-room.png\"\n", "display(Image(room_image_url, width=350))\n", "\n", "room_image = Part.from_uri(file_uri=room_image_url, mime_type=\"image/png\")\n", "\n", "prompt = \"Describe this room\"\n", "contents = [prompt, room_image]\n", "\n", "response = client.models.generate_content(model=MODEL_ID, contents=contents)\n", "display(Markdown(response.text))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "e28323d5679a"}, "outputs": [], "source": ["prompt1 = \"Recommend a new piece of furniture for this room\"\n", "prompt2 = \"Explain the reason in detail\"\n", "contents = [prompt1, room_image, prompt2]\n", "\n", "response = client.models.generate_content(model=MODEL_ID, contents=contents)\n", "display(Markdown(response.text))"]}, {"cell_type": "markdown", "metadata": {"id": "0a4d4c38c8af"}, "source": ["### Generating recommendations based on provided images\n", "\n", "Instead of keeping the recommendation open, you can also provide a list of items for the model to choose from. Here, you will load a few art images that the Gemini model can recommend. This is particularly useful for retail companies who want to provide product recommendations to users based on their current setup."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "434725a7c58f"}, "outputs": [], "source": ["art_image_urls = [\n", "    \"https://storage.googleapis.com/cloud-samples-data/generative-ai/image/room-art-1.png\",\n", "    \"https://storage.googleapis.com/cloud-samples-data/generative-ai/image/room-art-2.png\",\n", "    \"https://storage.googleapis.com/cloud-samples-data/generative-ai/image/room-art-3.png\",\n", "    \"https://storage.googleapis.com/cloud-samples-data/generative-ai/image/room-art-4.png\",\n", "]\n", "\n", "md_content = f\"\"\"\n", "|Customer photo |\n", "|:-----:|\n", "| <img src=\"{room_image_url}\" width=\"50%\"> |\n", "\n", "|Art 1| Art 2 | Art 3 | Art 4 |\n", "|:-----:|:----:|:-----:|:----:|\n", "| <img src=\"{art_image_urls[0]}\" width=\"60%\">|<img src=\"{art_image_urls[1]}\" width=\"100%\">|<img src=\"{art_image_urls[2]}\" width=\"60%\">|<img src=\"{art_image_urls[3]}\" width=\"60%\">|\n", "\"\"\"\n", "\n", "display(Markdown(md_content))\n", "\n", "# Load wall art images as Part objects\n", "art_images = [\n", "    Part.from_uri(file_uri=url, mime_type=\"image/png\") for url in art_image_urls\n", "]\n", "\n", "# To recommend an item from a selection, you will need to label the item number within the prompt.\n", "# That way you are providing the model with a way to reference each image as you pose a question.\n", "# Labeling images within your prompt also helps reduce hallucinations and produce better results.\n", "prompt = \"\"\"\n", "  You are an interior designer.\n", "  For each piece of wall art, explain whether it would be appropriate for the style of the room.\n", "  Rank each piece according to how well it would be compatible in the room.\n", "\"\"\"\n", "\n", "contents = [\n", "    \"Consider the following art pieces:\",\n", "    \"art 1:\",\n", "    art_images[0],\n", "    \"art 2:\",\n", "    art_images[1],\n", "    \"art 3:\",\n", "    art_images[2],\n", "    \"art 4:\",\n", "    art_images[3],\n", "    \"room:\",\n", "    room_image,\n", "    prompt,\n", "]\n", "\n", "response = client.models.generate_content(model=MODEL_ID, contents=contents)\n", "display(Markdown(response.text))"]}, {"cell_type": "markdown", "metadata": {"id": "4437b7608c8e"}, "source": ["## Understand entity relationships in technical diagrams\n", "\n", "Gemini has multimodal capabilities that enable it to understand diagrams and take actionable steps, such as optimization or code generation. This example demonstrates how Gemini can decipher an entity relationship (ER) diagram, understand the relationships between tables, identify requirements for optimization in a specific environment like BigQuery, and even generate corresponding code."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "klY4yBEiKmET"}, "outputs": [], "source": ["image_er_url = \"https://storage.googleapis.com/github-repo/img/gemini/multimodality_usecases_overview/er.png\"\n", "display(Image(image_er_url, width=350))\n", "\n", "prompt = \"Document the entities and relationships in this ER diagram.\"\n", "\n", "contents = [prompt, Part.from_uri(file_uri=image_er_url, mime_type=\"image/png\")]\n", "\n", "# Use a more deterministic configuration with a low temperature\n", "config = GenerateContentConfig(\n", "    temperature=0.1,\n", "    top_p=0.8,\n", "    top_k=40,\n", "    candidate_count=1,\n", "    max_output_tokens=8192,\n", ")\n", "\n", "response = client.models.generate_content(\n", "    model=MODEL_ID,\n", "    contents=contents,\n", "    config=config,\n", ")\n", "display(Markdown(response.text))"]}, {"cell_type": "markdown", "metadata": {"id": "ZBrdsvIU7Zkf"}, "source": ["## Compare images for similarities and differences\n", "\n", "Gemini can compare images and identify similarities or differences between objects.\n", "\n", "The following example shows two scenes from [Marienplatz in Munich, Germany](https://en.wikipedia.org/wiki/Marienplatz) that are slightly different."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "JUSJduLh8457"}, "outputs": [], "source": ["image_landmark1_url = \"https://storage.googleapis.com/github-repo/img/gemini/multimodality_usecases_overview/landmark1.jpg\"\n", "image_landmark2_url = \"https://storage.googleapis.com/github-repo/img/gemini/multimodality_usecases_overview/landmark2.jpg\"\n", "\n", "md_content = f\"\"\"\n", "| Image 1 | Image 2 |\n", "|:-----:|:----:|\n", "| <img src=\"{image_landmark1_url}\" width=\"350\"> | <img src=\"{image_landmark2_url}\" width=\"350\"> |\n", "\"\"\"\n", "\n", "display(Markdown(md_content))\n", "\n", "prompt1 = \"\"\"\n", "Consider the following two images:\n", "Image 1:\n", "\"\"\"\n", "prompt2 = \"\"\"\n", "Image 2:\n", "\"\"\"\n", "prompt3 = \"\"\"\n", "1. What is shown in Image 1? Where is it?\n", "2. What is similar between the two images?\n", "3. What is difference between Image 1 and Image 2 in terms of the contents or people shown?\n", "\"\"\"\n", "\n", "contents = [\n", "    prompt1,\n", "    Part.from_uri(file_uri=image_landmark1_url, mime_type=\"image/jpeg\"),\n", "    prompt2,\n", "    Part.from_uri(file_uri=image_landmark2_url, mime_type=\"image/jpeg\"),\n", "    prompt3,\n", "]\n", "\n", "config = GenerateContentConfig(\n", "    temperature=0.0,\n", "    top_p=0.8,\n", "    top_k=40,\n", "    candidate_count=1,\n", "    max_output_tokens=2048,\n", ")\n", "\n", "response = client.models.generate_content(\n", "    model=MODEL_ID,\n", "    contents=contents,\n", "    config=config,\n", ")\n", "display(Markdown(response.text))"]}], "metadata": {"colab": {"name": "intro_multimodal_use_cases.ipynb", "toc_visible": true}, "environment": {"kernel": "conda-base-py", "name": "workbench-notebooks.m129", "type": "gcloud", "uri": "us-docker.pkg.dev/deeplearning-platform-release/gcr.io/workbench-notebooks:m129"}, "kernelspec": {"display_name": "Python 3 (ipykernel) (Local)", "language": "python", "name": "conda-base-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 4}
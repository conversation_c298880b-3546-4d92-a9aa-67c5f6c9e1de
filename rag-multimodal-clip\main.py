from typing import Any
from pydantic import BaseModel
import fitz  # PyMuPDF
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
import uuid
from langchain.retrievers.multi_vector import MultiVectorRetriever
from langchain.storage import InMemoryStore
from langchain_chroma import Chroma
from langchain_core.documents import Document
from langchain_openai import OpenAIEmbeddings
from langchain_core.runnables import RunnablePassthrough
import os
from dotenv import load_dotenv
import openai
import base64  # Required for image encoding if you were to pass raw bytes to a multimodal LLM later
from langsmith import traceable
import shutil  # Import shutil for directory operations
import piexif
import google.generativeai as genai
import io
from PIL import Image
import json  

load_dotenv()

"""
-The Two-Layer System:

1. Vector Store (for search): Contains summaries
   - Summaries are embedded and stored in Chroma
   - When you ask "who is <PERSON><PERSON>?", it searches through these summaries
   - Summaries are more focused → better semantic matching
2. Document Store (for retrieval): Contains original content
   - Stores full original text and image paths
   - When relevant summaries are found, retrieves the original detailed content
   - Provides complete context to the final LLM

"""

# use open ai to create image metadata

# explore pillow for images

# Get absolute path to the PDF file in 'data' folder relative to your script
base_dir = os.path.dirname(
    os.path.abspath(__file__)
)  # directory of your running script
path = os.path.join(
    base_dir, "..", "cartoonData", "Doraemon_Character_Descriptions.pdf"
)  # ../data/visual_tuning.pdf


client = openai.OpenAI(api_key=os.environ.get(""))

# --- Create a directory to save extracted images ---
image_output_dir = os.path.join(base_dir, "extracted_images")
os.makedirs(image_output_dir, exist_ok=True)  # Create the directory if it doesn't exist

# --- Extract text and images with PyMuPDF ---
doc = fitz.open("./cartoonData/Doraemon_Character_Descriptions.pdf")
text_elements = []
image_elements = []  # List to store paths to extracted images

# Exract text
for page_num in range(len(doc)):
    page = doc.load_page(page_num)
    text = page.get_text("text")
    if text:
        text_elements.append(text)

# Extract images ---



def extract_images_from_pdf(pdf_path: str, image_output_dir: str = "extracted_images"):
    """Extract images from PDF, generate metadata, and save them with metadata."""
    os.makedirs(image_output_dir, exist_ok=True)
    doc = fitz.open(pdf_path)
 

    img_count = 0


    prompt = """
        You are an assistant specializing in the Doraemon cartoon series.

        You will be shown an image of the character: {character_name}.
        Important:
        - Always set "character_name" exactly to "{character_name}" (do not guess or change it).
        - Use the image only to describe appearance, expression, pose, and context.
        - Fill in all other fields based on known facts about {character_name} in the Doraemon series.

        Your task:
        Return a single JSON object with these exact keys:
        {
        "character_name": "{character_name}",
        "alias": "<other names or nicknames, if any>",
        "role_in_show": "<short role description>",
        "relationships": {
            "best_friend": "...",
            "crush": "...",
            "rival": "...",
            "classmates": ["...", "..."]
        },
        "appearance": {
            "clothing": "...",
            "expression": "...",
            "pose": "..."
        },
        "image_context": "<short description of the specific image shown>",
        "tags": ["keyword1", "keyword2", ...]
        }

        Rules:
        - Always include all keys, even if some values are null.
        - Do not output anything except valid JSON.
        """

    for page_num in range(len(doc)):
        page = doc[page_num]
        images = page.get_images(full=True)
        character_name =page.get_text()

        print("character_name===>",character_name)

        for img_index, img in enumerate(images):
            xref = img[0]
            base_image = doc.extract_image(xref)
            image_bytes = base_image["image"]
            image_ext = base_image["ext"]

            # Save image with metadata
            image_filename = (
                f"page_{page_num}_img_{img_index}_{uuid.uuid4().hex}.{image_ext}"
            )
            image_path = os.path.join(image_output_dir, image_filename)

            # Generate metadata using Gemini
            description = generate_metadata_with_gemini(image_bytes, prompt)

            # ✅ Display the image in notebook
            # img_display = Image.open(io.BytesIO(image_bytes))
            # display(img_display)

            image_elements.append(image_path)

            save_image_with_metadata(image_bytes, image_path, description)

            print(f"✅ Saved {image_path} with metadata: {description}")
            img_count += 1

    print(
        f"\n🎉 Done! Extracted and saved {img_count} images into '{image_output_dir}' folder."
    )


genai.configure(api_key="AIzaSyA3zurX0KH6ZURcu3cn77zPEZ4b_GEduf8")


def generate_metadata_with_gemini(image_bytes: bytes, prompt: str) -> str:
    """
    Send an image to Gemini 2.0 Flash to generate metadata/description.

    Args:
        image_bytes (bytes): Image file content in bytes.
        prompt (str): Instruction text for Gemini.

    Returns:
        str: Generated description or fallback message.
    """
    # Load model
    model = genai.GenerativeModel("gemini-2.0-flash")

    # Generate response with image + prompt
    response = model.generate_content(
        [prompt, {"mime_type": "image/jpeg", "data": image_bytes}]
    )

    return (
        response.text.strip()
        if response and getattr(response, "text", None)
        else "No description generated"
    )


def save_image_with_metadata(image_bytes: bytes, save_path: str, description: str):
    """Save image and embed description in EXIF metadata."""
    img = Image.open(io.BytesIO(image_bytes)).convert("RGB")
    exif_dict = {"0th": {}, "Exif": {}, "GPS": {}, "1st": {}, "thumbnail": None}

    # Put metadata into ImageDescription tag
    exif_dict["0th"][piexif.ImageIFD.ImageDescription] = description.encode(
        "utf-8", errors="ignore"
    )

    exif_bytes = piexif.dump(exif_dict)
    img.save(save_path, "JPEG", exif=exif_bytes)


# below function should only be called when extracted_images folder is empty
if len(os.listdir(image_output_dir)) == 0:
    extract_images_from_pdf("./cartoonData/characters_images.pdf", image_output_dir)
# --- Count categories ---
category_counts = {
    "text": len(text_elements),
    "image": len(image_elements),  # Include image count
}
print("Category counts:", category_counts)


# --- Define Element model ---
class Element(BaseModel):
    type: str
    text: Any
    image_path: str | None = None  # Added for image elements


# --- Categorize elements ---
categorized_elements = []

for text in text_elements:
    categorized_elements.append(Element(type="text", text=text))


# Add image elements to the categorized list
def encode_image_to_base64(image_path):
    with open(image_path, "rb") as f:
        return base64.b64encode(f.read()).decode("utf-8")


# create summaries out of image metadata


def read_image_metadata(image_path: str) -> str:
    """Read metadata (description) from image EXIF."""
    img = Image.open(image_path)
    exif_data = img.info.get("exif")
    if not exif_data:
        return None

    try:
        exif_dict = piexif.load(exif_data)
        desc = (
            exif_dict["0th"]
            .get(piexif.ImageIFD.ImageDescription, b"")
            .decode("utf-8", errors="ignore")
        )
        return desc if desc else None
    except Exception:
        return None


# Create summary of  image metadata

for idx, filename in enumerate(os.listdir("extracted_images")):
    if not filename.lower().endswith(".jpeg"):
        continue  # skip non-jpg files

    try:
        print(f"Processing image: {filename}")
        img_path = os.path.join("extracted_images", filename)

        if image_elements is None:
            image_elements.append(img_path)

        ext = img_path.split(".")[-1]

        # Open as PIL
        pil_image = Image.open(img_path).convert("RGB")

        # Create unique identifier
        image_id = f"img_{idx}_{filename}"

        # Encode as base64
        buffered = io.BytesIO()
        pil_image.save(buffered, format="PNG")
        img_base64 = base64.b64encode(buffered.getvalue()).decode()

        # Extract metadata from EXIF
        metadata_desc = read_image_metadata(img_path)

        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "You are an assistant tasked with creating a descriptive summary "
                            "that combines the provided metadata and the visual image. "
                            "make sure to include all the details in the metadata."
                            "Focus on: character name, role, key relationships, outfit, and any unique traits.\n\n"
                            f"Metadata:\n{json.dumps(metadata_desc, ensure_ascii=False, indent=2)}",
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/{ext};base64,{img_base64}"
                            },
                        },
                    ],
                }
            ],
            max_tokens=300,
        )
        summary = response.choices[0].message.content
        print("summary===>", summary)
        categorized_elements.append(
            Element(type="image", text=summary, image_path=img_path)
        )

        print(f"✅ Processed {filename} | Metadata: {metadata_desc}")

    except Exception as e:
        print(f"❌ Error processing {filename}: {e}")
        continue


# for image_path in image_elements:
#     try:
#         print(f"Processing image: {image_path}")
#         ext = image_path.split('.')[-1]  # e.g., 'jpeg' or 'png'
#         image_base64 = encode_image_to_base64(image_path)

#         response = client.chat.completions.create(
#             model="gpt-4o",
#             messages=[
#                 {
#                     "role": "user",
#                     "content": [
#                         {"type": "text", "text": "You are an assistant tasked with summarizing image. \
# Give a concise summary of the provided image."},
#                         {
#                             "type": "image_url",
#                             "image_url": {
#                                 "url": f"data:image/{ext};base64,{image_base64}"
#                             },
#                         },
#                     ],
#                 }
#             ],
#             max_tokens=300,
#         )
#         summary = response.choices[0].message.content
#         print("summary===>",summary)
#         categorized_elements.append(Element(type="image", text=summary, image_path=image_path))

#     except Exception as e:
#         print(f"Error processing image {image_path}: {e}")


# --- Separate lists by type ---
text_elements = [e for e in categorized_elements if e.type == "text"]
image_elements = [
    e for e in categorized_elements if e.type == "image"
]  # New list for images

print("Text count:", len(text_elements))
print("Images count:", len(image_elements))


# --- Prompt for summary ---
# The prompt for summarization is still text-based, even for images, as we're summarizing to text.
prompt_text = """You are an assistant tasked with summarizing tables, text, and descriptions of images. \
Give a concise summary of the provided content. Content chunk: {element} """
prompt = ChatPromptTemplate.from_template(prompt_text)

# --- Setup summarization chain ---
# Using gpt-4 for summarization. Note: If you wanted truly *visual* summaries,
# you'd need a multimodal model here (e.g., gpt-4o) and different input format.
model = ChatOpenAI(
    temperature=0, model="gpt-4o", api_key=os.environ.get("OPENAI_API_KEY")
)
summarize_chain = {"element": lambda x: x} | prompt | model | StrOutputParser()

# --- Summarize texts, tables, and images ---
text_summaries = summarize_chain.batch(
    [e.text for e in text_elements], {"max_concurrency": 5}
)
# For images, we summarize the placeholder text. For better results, you'd feed actual image descriptions from a Vision LLM here.
# image_summaries = summarize_chain.batch([e.text for e in image_elements], {"max_concurrency": 5})
image_summaries = [e.text for e in image_elements]


# --- Setup vectorstore and retriever ---

# The vectorstore to use to index the child chunks
vectorstore = Chroma(
    collection_name="summaries",
    embedding_function=OpenAIEmbeddings(api_key=os.environ.get("OPENAI_API_KEY")),
)

# The storage layer for the parent documents
store = InMemoryStore()
id_key = "doc_id"  # key to use to establish connection between summaries (vector store) and original content (doc store)

# The retriever (empty to start)
retriever = MultiVectorRetriever(
    vectorstore=vectorstore,
    docstore=store,
    id_key=id_key
)

# --- Add summarized texts ---
doc_ids = [str(uuid.uuid4()) for _ in text_elements]
summary_texts_docs = [
    Document(page_content=s, metadata={id_key: doc_ids[i], "type": "text_summary"})
    for i, s in enumerate(text_summaries)
]
retriever.vectorstore.add_documents(summary_texts_docs)
# Store original text content
retriever.docstore.mset(
    list(
        zip(
            doc_ids,
            [
                Document(page_content=e.text, metadata={"type": "original_text"})
                for e in text_elements
            ],
        )
    )
)


# --- Add summarized images ---
image_ids = [str(uuid.uuid4()) for _ in image_elements]
summary_images_docs = [
    Document(page_content=s, metadata={id_key: image_ids[i], "type": "image_summary"})
    for i, s in enumerate(image_summaries)
]

retriever.vectorstore.add_documents(summary_images_docs)
# Store original image paths in the docstore
# The MultiVectorRetriever will retrieve these Document objects.
# The page_content is the image_path.
retriever.docstore.mset(
    list(
        zip(
            image_ids,
            [
                Document(
                    page_content=e.image_path, metadata={"type": "original_image_path"}
                )
                for e in image_elements
            ],
        )
    )
)


# --- Final RAG prompt ---
# This template needs to be flexible enough for multimodal context.
# However, since the retriever only returns text content (original text/table strings and image paths),
# the final LLM will still process text.
template = """Answer the question based only on the following context, which can include text, and references to images.
If an image is relevant, mention its filename or a descriptive reference.

Context:
{context}

Question: {question}
"""
prompt = ChatPromptTemplate.from_template(template)

# --- RAG pipeline ---
# The model is still ChatOpenAI with gpt-4, which is text-only.
# To make it truly Option 3, this model would need to be gpt-4o or a similar multimodal LLM
# and the 'context' would need to be formatted as a list of content parts including image_url.
model = ChatOpenAI(
    temperature=0, model="gpt-4o", api_key=os.environ.get("OPENAI_API_KEY")
)


# Custom formatting for context to explicitly show image paths
def _format_context(docs):
    formatted_docs = []
    for doc in docs:
        if doc.metadata.get("type") == "original_image_path":
            print("doc.page_content==>", doc)

            # For image paths, just list the path or a reference
            formatted_docs.append(
                f"Image Reference: {os.path.basename(doc.page_content)}"
            )
        else:
            formatted_docs.append(doc.page_content)
    return "\n\n".join(formatted_docs)


chain = (
    {"context": retriever | _format_context, "question": RunnablePassthrough()}
    | prompt
    | model
    | StrOutputParser()
)

# # --- Invoke with your question ---
# table - Can you compare the different models listed in the table in terms of their context handling capabilities?
#         Can you summarize the differences between the models shown in the table in terms of input modalities?
# image - Which image involves a question about yogurt flavor? tell me what is shown exactly in there
#         Is there any image in the dataset where the actions seem unsafe or unconventional?
# answer = chain.invoke(
#     "who is gian ? give me his image and his personality traits ?"
# )

queries = [
       "List all characters you can find in both text and images.",
    "What images do you have access to?",
    "What text content do you have about characters?",
    "Show me the connection between text descriptions and available images."
]

for query in queries:
    answer = chain.invoke(query)
    print(f"Answer: {answer}")



## --- Deleting the Entire Directory ---


# # --- Cleanup: Delete the entire extracted_images directory ---
# try:
#     if os.path.exists(image_output_dir): # Check if the directory exists before attempting to delete
#         shutil.rmtree(image_output_dir)
#         print(f"Deleted directory: {image_output_dir}")
# except OSError as e:
#     print(f"Error deleting directory {image_output_dir}: {e}")

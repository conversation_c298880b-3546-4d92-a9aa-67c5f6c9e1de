# ============================================================================
# MULTIMODAL RAG SYSTEM FOR DORAEMON CHARACTER ANALYSIS
# ============================================================================
# This system implements a Retrieval Augmented Generation (RAG) pipeline that
# can process both text and images from PDF documents, specifically designed
# for analyzing Doraemon cartoon characters.

# ============================================================================
# IMPORTS AND DEPENDENCIES
# ============================================================================

from typing import Any
from pydantic import BaseModel
import fitz  # PyMuPDF - for PDF processing and image extraction
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
import uuid  # for generating unique identifiers
from langchain.retrievers.multi_vector import MultiVectorRetriever  # core RAG component
from langchain.storage import InMemoryStore  # document storage
from langchain_chroma import Chroma  # vector database
from langchain_core.documents import Document
from langchain_openai import OpenAIEmbeddings  # text embeddings
from langchain_core.runnables import RunnablePassthrough
import os
from dotenv import load_dotenv  # environment variable management
import openai  # OpenAI API client
import base64  # for image encoding
from langsmith import traceable  # for tracing/debugging
import shutil  # for directory operations
import piexif  # for EXIF metadata manipulation
import google.generativeai as genai  # Google Gemini API
import io
from PIL import Image  # image processing
import json  # JSON handling
import glob  # for file globbing
import time

# Load environment variables from .env file
load_dotenv()

# ============================================================================
# SYSTEM ARCHITECTURE OVERVIEW
# ============================================================================
"""
This system implements a Two-Layer Multimodal RAG Architecture:

1. Vector Store (for search): Contains summaries
   - Summaries are embedded and stored in Chroma vector database
   - When you ask "who is Gian?", it searches through these summaries
   - Summaries are more focused → better semantic matching for retrieval

2. Document Store (for retrieval): Contains original content
   - Stores full original text and image paths with detailed information
   - When relevant summaries are found, retrieves the original detailed content
   - Provides complete context to the final LLM for comprehensive answers

This approach combines the efficiency of summary-based search with the
completeness of full document retrieval.
"""

# ============================================================================
# FILE PATH CONFIGURATION
# ============================================================================

# Get absolute path to the PDF file in 'cartoonData' folder relative to script
base_dir = os.path.dirname(
    os.path.abspath(__file__)
)  # Get directory of the current running script
path = os.path.join(
    base_dir, "..", "carData", "vehicle_descriptions_expanded.pdf"
)  # Navigate to ../cartoonData/Doraemon_Character_Descriptions.pdf

# ============================================================================
# API CLIENT INITIALIZATION
# ============================================================================

# Initialize OpenAI client for embeddings and chat completions
client = openai.OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))

# ============================================================================
# IMAGE EXTRACTION SETUP
# ============================================================================

# Create a directory to save extracted images from PDF
image_output_dir = os.path.join(base_dir, "extracted_images_with_metadata_small")
os.makedirs(image_output_dir, exist_ok=True)  # Create directory if it doesn't exist

# ============================================================================
# PDF PROCESSING AND CONTENT EXTRACTION
# ============================================================================

# Open the PDF document using PyMuPDF (fitz)
doc = fitz.open("./carData/vehicle_descriptions_expanded.pdf")
text_elements = []  # List to store extracted text from each page
image_elements = []  # List to store paths to extracted images

# ============================================================================
# TEXT EXTRACTION FROM PDF
# ============================================================================

# Extract text content from each page of the PDF
for page_num in range(len(doc)):
    page = doc.load_page(page_num)  # Load the current page
    text = page.get_text("text")  # Extract all text from the page
    if text:  # Only add non-empty text
        text_elements.append(text)  # Add text to our collection

# ============================================================================
# IMAGE EXTRACTION AND METADATA GENERATION FUNCTION
# ============================================================================


def extract_images_from_folder(
    image_folder_path: str, image_output_dir: str = "extracted_images_with_metadata_small"
):
    """
    Process images from a folder, generate rich metadata using Gemini AI, and save them.

    This function:
    1. Reads all images from the specified folder
    2. Uses Gemini AI to analyze each image and generate detailed metadata
    3. Saves images with comprehensive character information
    4. Returns structured data for RAG system integration

    Args:
        image_folder_path (str): Path to the folder containing images
        image_output_dir (str): Directory to save processed images with metadata

    Returns:
        List of image paths with metadata
    """
    os.makedirs(image_output_dir, exist_ok=True)  # Create output directory

    # Supported image extensions
    supported_extensions = [".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".webp"]

    # Get all image files from the folder
    image_files = []
    for ext in supported_extensions:
        image_files.extend(glob.glob(os.path.join(image_folder_path, f"*{ext}")))
        # image_files.extend(glob.glob(os.path.join(image_folder_path, f"*{ext.upper()}")))

    img_count = 0  # Counter for unique image naming
    print("image_files===>", image_files)

    # ============================================================================
    # GEMINI AI PROMPT FOR CHARACTER ANALYSIS
    # ============================================================================

    # Updated prompt for general vehicle/car analysis instead of Doraemon
    prompt = """
        You are an assistant specializing in vehicle and automotive analysis.

        You will be shown an image of a vehicle.
        Important:
        - Analyze the image to identify the vehicle type, make, model if possible
        - Describe the vehicle's appearance, condition, and context
        - Provide relevant automotive details

        Your task:
        Return a single JSON object with these exact keys:
        {
        "vehicle_type": "<car, truck, motorcycle, etc.>",
        "make_model": "<brand and model if identifiable>",
        "color": "<primary color of the vehicle>",
        "condition": "<new, used, damaged, etc.>",
        "features": {
            "body_style": "...",
            "doors": "...",
            "special_features": "..."
        },
        "setting": "<where the vehicle is located/context>",
        "image_context": "<detailed description of what's shown in the image>",
        "tags": ["keyword1", "keyword2", ...]
        }

        Rules:
        - Always include all keys, even if some values are null or "unknown"
        - Do not output anything except valid JSON
        - Be descriptive and accurate based on what you can see
        """

    # ============================================================================
    # PROCESS EACH IMAGE FILE
    # ============================================================================

    for image_file_path in image_files:
        try:
            print(f"Processing: {os.path.basename(image_file_path)}")

            # Read image bytes
            with open(image_file_path, "rb") as f:
                image_bytes = f.read()

            # Get file extension
            image_ext = os.path.splitext(image_file_path)[1][1:]  # Remove the dot

            # ============================================================================
            # GENERATE UNIQUE FILENAME AND PATH
            # ============================================================================

            # Create unique filename with original name and UUID
            original_name = os.path.splitext(os.path.basename(image_file_path))[0]
            image_filename = f"{original_name}_{uuid.uuid4().hex}.{image_ext}"
            image_path = os.path.join(image_output_dir, image_filename)

            # ============================================================================
            # GENERATE AI-POWERED METADATA
            # ============================================================================

            # Use Gemini AI to analyze the image and generate rich metadata
            description = generate_metadata_with_openai(image_bytes, prompt,image_ext)

            # Add image path to our collection for later use in RAG system
            image_elements.append(image_path)

            # ============================================================================
            # SAVE IMAGE WITH METADATA
            # ============================================================================

            # Save the image file along with its AI-generated metadata
            save_image_with_metadata(image_bytes, image_path, description)

            print(f"✅ Saved {image_path} with metadata: {description}")
            img_count += 1  # Increment counter

            # Add a delay to prevent hitting the API rate limit
            print("⏳ Adding a 3-second delay...")
            time.sleep(5)  # Pauses for 3 seconds

        except Exception as e:
            print(f"❌ Error processing {image_file_path}: {e}")
            continue

    # Final summary of extraction process
    print(
        f"\n🎉 Done! Processed and saved {img_count} images into '{image_output_dir}' folder."
    )
    return image_elements


# ============================================================================
# GEMINI AI CONFIGURATION
# ============================================================================

# Configure Gemini AI with API key (Note: Should use environment variable in production)
genai.configure(api_key=os.environ.get("GEMINI_API_KEY"))


def generate_metadata_with_openai(image_bytes: bytes, prompt: str,ext:str) -> str:
    """
    Generate rich metadata for images using Google's Gemini AI model.

    This function sends an image to Gemini 2.0 Flash model along with a detailed
    prompt to analyze the image and generate structured metadata about Doraemon
    characters, including their relationships, appearance, and context.

    Args:
        image_bytes (bytes): Raw image data in bytes format
        prompt (str): Detailed instruction prompt for the AI model

    Returns:
        str: AI-generated JSON metadata describing the character and image
    """
    # Initialize the Gemini 2.0 Flash model for multimodal analysis

    #get img base64
    img_base64 = base64.b64encode(image_bytes).decode("utf-8")

    # instead of gemini use open api
    response = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt,
                    },
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/{ext};base64,{img_base64}"},
                    },
                ],
            }
        ],
    )

   # model = genai.GenerativeModel("gemini-2.0-flash")

    # # Send both the prompt and image to Gemini for analysis
    # response = model.generate_content(
    #     [prompt, {"mime_type": "image/jpeg", "data": image_bytes}]
    # )

    response = response.choices[0].message.content

    # Return the AI-generated metadata with error handling
    # return (
    #     response.text.strip()
    #     if response and getattr(response, "text", None)
    #     else "No description generated"
    # )
    return response


def save_image_with_metadata(image_bytes: bytes, save_path: str, description: str):
    """
    Save image file with embedded metadata in EXIF format.

    This function takes image bytes and embeds the AI-generated description
    directly into the image file's EXIF metadata, making the metadata
    permanently associated with the image file.

    Args:
        image_bytes (bytes): Raw image data
        save_path (str): File path where image should be saved
        description (str): AI-generated metadata to embed in image
    """
    # Convert bytes to PIL Image object and ensure RGB format
    img = Image.open(io.BytesIO(image_bytes)).convert("RGB")

    # Create EXIF dictionary structure for metadata storage
    exif_dict = {"0th": {}, "Exif": {}, "GPS": {}, "1st": {}, "thumbnail": None}

    # Embed the AI-generated description in the ImageDescription EXIF tag
    exif_dict["0th"][piexif.ImageIFD.ImageDescription] = description.encode(
        "utf-8", errors="ignore"  # Handle encoding errors gracefully
    )

    # Convert EXIF dictionary to bytes and save image with metadata
    exif_bytes = piexif.dump(exif_dict)
    img.save(save_path, "JPEG", exif=exif_bytes)


# ============================================================================
# CONDITIONAL IMAGE EXTRACTION
# ============================================================================

# Only extract images if the output directory is empty (avoid reprocessing)
if len(os.listdir(image_output_dir)) == 0:
    extract_images_from_folder("./carData/carImg", image_output_dir)

# ============================================================================
# CONTENT CATEGORIZATION AND STATISTICS
# ============================================================================

# Count the different types of content extracted from the PDF
category_counts = {
    "text": len(text_elements),  # Number of text pages/sections
    "image": len(image_elements),  # Number of images extracted
}
print("Category counts:", category_counts)

# ============================================================================
# DATA MODEL DEFINITION
# ============================================================================


# Define Pydantic model for structured element representation
class Element(BaseModel):
    """
    Data model for representing different types of content elements.

    Attributes:
        type (str): Type of element ('text' or 'image')
        text (Any): Text content or metadata
        image_path (str, optional): Path to image file for image elements
    """

    type: str
    text: Any
    image_path: str | None = None  # Optional field for image file paths


# ============================================================================
# ELEMENT CATEGORIZATION
# ============================================================================

# Create structured list of all content elements
categorized_elements = []

# Add all text elements to the categorized list
for text in text_elements:
    categorized_elements.append(Element(type="text", text=text))

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================


def encode_image_to_base64(image_path):
    """
    Convert image file to base64 encoded string.

    Args:
        image_path (str): Path to the image file

    Returns:
        str: Base64 encoded image data
    """
    with open(image_path, "rb") as f:
        return base64.b64encode(f.read()).decode("utf-8")


def read_image_metadata(image_path: str) -> str:
    """
    Extract AI-generated metadata from image EXIF data.

    This function reads the metadata that was embedded in the image file
    during the extraction process, containing rich character information
    generated by Gemini AI.

    Args:
        image_path (str): Path to the image file

    Returns:
        str: Extracted metadata description or None if not found
    """
    img = Image.open(image_path)  # Open the image file
    exif_data = img.info.get("exif")  # Get EXIF data from image

    if not exif_data:
        return None  # No EXIF data found

    try:
        # Parse EXIF data and extract ImageDescription field
        exif_dict = piexif.load(exif_data)
        desc = (
            exif_dict["0th"]
            .get(piexif.ImageIFD.ImageDescription, b"")  # Get description bytes
            .decode("utf-8", errors="ignore")  # Convert to string
        )
        return desc if desc else None  # Return description or None
    except Exception:
        return None  # Handle any parsing errors gracefully


# ============================================================================
# IMAGE METADATA PROCESSING AND SUMMARY GENERATION
# ============================================================================

# Process all extracted images to create comprehensive summaries
for idx, filename in enumerate(os.listdir("extracted_images_with_metadata_small")):
    # Only process JPEG image files
    if not filename.lower().endswith(".jpg"):
        continue  # Skip non-JPEG files

    try:
        print(f"Processing image: {filename}")
        img_path = os.path.join("extracted_images_with_metadata_small", filename)

        # Add image path to elements list if not already present
        if image_elements is None:
            image_elements.append(img_path)

        # Get file extension for proper MIME type handling
        ext = img_path.split(".")[-1]

        # ============================================================================
        # IMAGE PREPROCESSING
        # ============================================================================

        # Open and convert image to RGB format for consistency
        pil_image = Image.open(img_path).convert("RGB")

        # Create unique identifier for this image
        image_id = f"img_{idx}_{filename}"

        # Convert image to base64 for API transmission
        buffered = io.BytesIO()
        pil_image.save(buffered, format="PNG")
        img_base64 = base64.b64encode(buffered.getvalue()).decode()

        # ============================================================================
        # METADATA EXTRACTION AND SUMMARY GENERATION
        # ============================================================================

        # Extract the AI-generated metadata from image EXIF data
        metadata_desc = read_image_metadata(img_path)

        # Use GPT-4o to create a comprehensive summary combining metadata and visual analysis
        response = client.chat.completions.create(
            model="gpt-4o",  # Use GPT-4o for multimodal analysis
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "You are an assistant tasked with creating a descriptive summary "
                            "that combines the provided metadata and the visual image. "
                            "Make sure to include all the details in the metadata. "
                            "Focus on: character name, role, key relationships, outfit, and any unique traits.\n\n"
                            f"Metadata:\n{json.dumps(metadata_desc, ensure_ascii=False, indent=2)}",
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/{ext};base64,{img_base64}"
                            },
                        },
                    ],
                }
            ],
            max_tokens=300,  # Limit response length for concise summaries
        )

        # Extract the generated summary from the API response
        summary = response.choices[0].message.content
        print("summary===>", summary)

        # Add the processed image element to our categorized collection
        categorized_elements.append(
            Element(type="image", text=summary, image_path=img_path)
        )

        print(f"✅ Processed {filename} | Metadata: {metadata_desc}")

    except Exception as e:
        print(f"❌ Error processing {filename}: {e}")
        continue  # Skip this image and continue with the next one


# ============================================================================
# ELEMENT SEPARATION BY TYPE
# ============================================================================

# Separate the processed elements into text and image categories for RAG system
text_elements = [e for e in categorized_elements if e.type == "text"]
image_elements = [
    e for e in categorized_elements if e.type == "image"
]  # Processed image elements with summaries

# Display processing statistics
print("Text count:", len(text_elements))
print("Images count:", len(image_elements))

# ============================================================================
# SUMMARIZATION SETUP
# ============================================================================

# Define prompt template for content summarization
# This prompt works for both text and image descriptions, creating concise summaries
prompt_text = """You are an assistant tasked with summarizing tables, text, and descriptions of images. \
Give a concise summary of the provided content. Content chunk: {element} """
prompt = ChatPromptTemplate.from_template(prompt_text)

# ============================================================================
# LANGUAGE MODEL CHAIN SETUP
# ============================================================================

# Initialize GPT-4o model for summarization tasks
# Note: For truly visual summaries, you'd need multimodal input format
model = ChatOpenAI(
    temperature=0,  # Deterministic output for consistent summaries
    model="gpt-4o",  # Use GPT-4o for high-quality summarization
    api_key=os.environ.get("OPENAI_API_KEY"),
)

# Create summarization chain using LangChain's pipeline syntax
summarize_chain = {"element": lambda x: x} | prompt | model | StrOutputParser()

# ============================================================================
# CONTENT SUMMARIZATION
# ============================================================================

# Generate summaries for text elements using batch processing for efficiency
text_summaries = summarize_chain.batch(
    [e.text for e in text_elements], {"max_concurrency": 5}
)

# For images, use the already processed summaries from GPT-4o analysis
# These summaries already combine visual analysis with metadata
image_summaries = [e.text for e in image_elements]

# ============================================================================
# VECTOR STORE AND RETRIEVAL SYSTEM SETUP
# ============================================================================

# Initialize Chroma vector database for storing and searching summaries
vectorstore = Chroma(
    collection_name="summaries",  # Collection name for organized storage
    embedding_function=OpenAIEmbeddings(api_key=os.environ.get("OPENAI_API_KEY")),
    # persist_directory="chroma_db",  # Directory to persist the database
    # distance_function="",  # Distance metric for similarity search
)

# Initialize in-memory document store for original content
store = InMemoryStore()
id_key = (
    "doc_id"  # Key to link summaries (vector store) with original content (doc store)
)

# ============================================================================
# MULTI-VECTOR RETRIEVER INITIALIZATION
# ============================================================================

# Create the multi-vector retriever that implements the two-layer system
retriever = MultiVectorRetriever(
    vectorstore=vectorstore,  # For similarity search on summaries
    docstore=store,  # For storing original detailed content
    id_key=id_key,
    k=6,  # Connection key between the two stores
  
)

# ============================================================================
# POPULATE VECTOR STORE WITH TEXT SUMMARIES
# ============================================================================

# Generate unique IDs for each text element
doc_ids = [str(uuid.uuid4()) for _ in text_elements]

# Create Document objects for text summaries with metadata
summary_texts_docs = [
    Document(page_content=s, metadata={id_key: doc_ids[i], "type": "text_summary"})
    for i, s in enumerate(text_summaries)
]

# Add text summaries to vector store for similarity search
retriever.vectorstore.add_documents(summary_texts_docs)

# Store original full text content in document store
# This creates the link between summaries (searchable) and original content (retrievable)
retriever.docstore.mset(
    list(
        zip(
            doc_ids,  # Same IDs used in vector store
            [
                Document(page_content=e.text, metadata={"type": "original_text"})
                for e in text_elements
            ],
        )
    )
)

# ============================================================================
# POPULATE VECTOR STORE WITH IMAGE SUMMARIES
# ============================================================================

# Generate unique IDs for each image element
image_ids = [str(uuid.uuid4()) for _ in image_elements]

# Create Document objects for image summaries with metadata
summary_images_docs = [
    Document(page_content=s, metadata={id_key: image_ids[i], "type": "image_summary"})
    for i, s in enumerate(image_summaries)
]

# Add image summaries to vector store for similarity search
retriever.vectorstore.add_documents(summary_images_docs)

# Store original image paths in document store
# The MultiVectorRetriever will retrieve these Document objects containing image paths
# This allows the system to access the actual image files when needed
retriever.docstore.mset(
    list(
        zip(
            image_ids,  # Same IDs used in vector store
            [
                Document(
                    page_content=e.image_path, metadata={"type": "original_image_path"}
                )
                for e in image_elements
            ],
        )
    )
)


# ============================================================================
# RAG QUERY PROCESSING SETUP
# ============================================================================

# Define the prompt template for answering questions using retrieved context
# This template handles both text content and image references
template = """Answer the question based only on the following context, which can include text, and references to images.
If an image is relevant, mention its filename or a descriptive reference.

Context:
{context}

Question: {question}
"""
prompt = ChatPromptTemplate.from_template(template)

# ============================================================================
# LANGUAGE MODEL FOR FINAL ANSWER GENERATION
# ============================================================================

# Initialize GPT-4o model for final answer generation
# Note: For true multimodal processing, the context would need to include actual images
model = ChatOpenAI(
    temperature=0,  # Deterministic responses
    model="gpt-4o",  # Use GPT-4o for high-quality answers
    api_key=os.environ.get("OPENAI_API_KEY"),
)

# ============================================================================
# CONTEXT FORMATTING FUNCTION
# ============================================================================


def _format_context(docs):
    """
    Format retrieved documents for presentation to the language model.

    This function processes both text content and image references,
    ensuring that image paths are presented in a readable format.

    Args:
        docs: List of Document objects retrieved by the system

    Returns:
        str: Formatted context string for the language model
    """
    formatted_docs = []
    for doc in docs:
        if doc.metadata.get("type") == "original_image_path":
            print("doc.page_content==>", doc)  # Debug output

            # For image paths, create a readable reference
            formatted_docs.append(
                f"Image Reference: {os.path.basename(doc.page_content)}"
            )
        else:
            # For text content, use as-is
            formatted_docs.append(doc.page_content)

    return "\n\n".join(formatted_docs)


# ============================================================================
# COMPLETE RAG PIPELINE CHAIN
# ============================================================================

# Create the complete RAG pipeline using LangChain's pipeline syntax
chain = (
    {"context": retriever | _format_context, "question": RunnablePassthrough()}
    | prompt  # Apply the question-answering prompt
    | model  # Generate answer using GPT-4o
    | StrOutputParser()  # Extract text from model response
)

# ============================================================================
# EXAMPLE QUERIES AND SYSTEM TESTING
# ============================================================================

# Example queries that demonstrate different capabilities of the system:
# - Table analysis: "Can you compare the different models listed in the table in terms of their context handling capabilities?"
# - Image analysis: "Which image involves a question about yogurt flavor? tell me what is shown exactly in there"
# - Character queries: "who is gian ? give me his image and his personality traits ?"

# Single query example (commented out):
# answer = chain.invoke("who is gian ? give me his image and his personality traits ?")

# ============================================================================
# BATCH QUERY PROCESSING
# ============================================================================

# Define a set of test queries to demonstrate system capabilities
queries = [
    "What is the starting price range for the Porsche 911 Targa 4 (992)?",
    "What is the engine size and horsepower of the 2021 Toyota Corolla E210?",
    "What is the brand and vehicle type of the Mazda MX-5?",
    "How does the Rolls-Royce reflect its ultra-luxury status in the image, particularly the craftsmanship and design details?",
    "What is the engine specification of the Audi A6 TDI 2025, and how does its design reflect this performance?"
    "How does the Hyundai Creta’s exterior design match its function as a versatile urban family car? show me the image of car from all angles too"
    "Describe how the Tata Safari’s design and specifications support its suitability for long-distance travel and family use. give image to",
    "How does the visual appearance of the Hyundai Creta reflect its subcompact SUV category? give image too"
]

# Process each query and display results
for query in queries:
    answer = chain.invoke(query)  # Execute the RAG pipeline
    print(f"Query: {query}")
    print(f"Answer: {answer}")
    print("-" * 80)  # Separator for readability

# ============================================================================
# CLEANUP SECTION (OPTIONAL)
# ============================================================================

# Optional cleanup to remove extracted images directory
# Uncomment the following code if you want to clean up after processing

# try:
#     if os.path.exists(image_output_dir):  # Check if directory exists
#         shutil.rmtree(image_output_dir)   # Remove entire directory
#         print(f"✅ Cleaned up directory: {image_output_dir}")
# except OSError as e:
#     print(f"❌ Error deleting directory {image_output_dir}: {e}")

# ============================================================================
# END OF MULTIMODAL RAG SYSTEM
# ============================================================================

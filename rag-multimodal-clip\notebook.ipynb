%pip install --quiet pymupdf
%pip install --quiet torch
%pip install --quiet pillow
%pip install --quiet transformers
%pip install --quiet langchain
%pip install --quiet numpy
%pip install --quiet sklearn
%pip install --quiet langchain-openai


%pip install langchain_chroma

from typing import Any
from pydantic import BaseModel
import fitz  # PyMuPDF

from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
import uuid
from langchain.retrievers.multi_vector import MultiVectorRetriever
from langchain.storage import InMemoryStore
from langchain_chroma import Chroma
from langchain_core.documents import Document
from langchain_openai import OpenAIEmbeddings
from langchain_core.runnables import RunnablePassthrough
import os
from dotenv import load_dotenv
import openai
import base64 # Required for image encoding if you were to pass raw bytes to a multimodal LLM later
import shutil # Import shutil for directory operations
# ============================================================================
# MULTIMODAL RAG SYSTEM WITH DATA PERSISTENCE
# ============================================================================
# This system implements a Retrieval Augmented Generation (RAG) pipeline that
# can process both text and images from PDF documents, with intelligent caching
# to avoid expensive reprocessing on subsequent runs.

# ============================================================================
# IMPORTS AND DEPENDENCIES
# ============================================================================

from typing import Any
from pydantic import BaseModel
import fitz  # PyMuPDF - for PDF processing and image extraction
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
import uuid  # for generating unique identifiers
from langchain.retrievers.multi_vector import MultiVectorRetriever  # core RAG component
from langchain.storage import InMemoryStore  # document storage
from langchain_chroma import Chroma  # vector database
from langchain_core.documents import Document
from langchain_openai import OpenAIEmbeddings  # text embeddings
from langchain_core.runnables import RunnablePassthrough
import os
from dotenv import load_dotenv  # environment variable management
import openai  # OpenAI API client
import base64  # for image encoding
from langsmith import traceable  # for tracing/debugging
import shutil  # for directory operations
import piexif  # for EXIF metadata manipulation
import google.generativeai as genai  # Google Gemini API
import io
from PIL import Image  # image processing
import json  # JSON handling
import glob  # for file globbing
import time
import logging

# Import our data manager for caching
from data_manager import DataManager

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ============================================================================
# SYSTEM ARCHITECTURE OVERVIEW
# ============================================================================
"""
This system implements a Two-Layer Multimodal RAG Architecture with Caching:

1. Vector Store (for search): Contains summaries
   - Summaries are embedded and stored in Chroma vector database
   - When you ask "who is Gian?", it searches through these summaries
   - Summaries are more focused → better semantic matching for retrieval

2. Document Store (for retrieval): Contains original content
   - Stores full original text and image paths with detailed information
   - When relevant summaries are found, retrieves the original detailed content
   - Provides complete context to the final LLM for comprehensive answers

3. Data Persistence Layer: Intelligent caching system
   - Saves processed data to avoid expensive reprocessing
   - Validates cache freshness based on source file changes
   - Loads from cache on subsequent runs for instant startup

This approach combines the efficiency of summary-based search with the
completeness of full document retrieval and the speed of intelligent caching.
"""

# ============================================================================
# FILE PATH CONFIGURATION
# ============================================================================

# Get absolute path to the PDF file in 'carData' folder relative to script
base_dir = os.path.dirname(os.path.abspath(__file__))
pdf_path = "./carData/vehicle_descriptions_expanded.pdf"
image_dir = "./carData/carImg"
image_output_dir = os.path.join(base_dir, "extracted_images_with_metadata")

# Initialize data manager for caching
data_manager = DataManager(cache_dir="data_cache")

# ============================================================================
# API CLIENT INITIALIZATION
# ============================================================================

# Initialize OpenAI client for embeddings and chat completions
client = openai.OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))

# Configure Gemini AI
genai.configure(api_key=os.environ.get("GEMINI_API_KEY"))

# ============================================================================
# DATA MODEL DEFINITION
# ============================================================================

class Element(BaseModel):
    """
    Data model for representing different types of content elements.

    Attributes:
        type (str): Type of element ('text' or 'image')
        text (Any): Text content or metadata
        image_path (str, optional): Path to image file for image elements
    """
    type: str
    text: Any
    image_path: str | None = None

# ============================================================================
# UTILITY FUNCTIONS (from original main.py)
# ============================================================================

def encode_image_to_base64(image_path):
    """Convert image file to base64 encoded string."""
    with open(image_path, "rb") as f:
        return base64.b64encode(f.read()).decode("utf-8")

def read_image_metadata(image_path: str) -> str:
    """Extract AI-generated metadata from image EXIF data."""
    try:
        img = Image.open(image_path)
        exif_data = img.info.get("exif")
        if not exif_data:
            return None

        exif_dict = piexif.load(exif_data)
        desc = (
            exif_dict["0th"]
            .get(piexif.ImageIFD.ImageDescription, b"")
            .decode("utf-8", errors="ignore")
        )
        return desc if desc else None
    except Exception:
        return None

def generate_metadata_with_gemini(image_bytes: bytes, prompt: str) -> str:
    """Generate rich metadata for images using Google's Gemini AI model."""
    try:
        model = genai.GenerativeModel("gemini-2.0-flash")
        response = model.generate_content([
            prompt,
            {"mime_type": "image/jpeg", "data": image_bytes}
        ])
        return (
            response.text.strip()
            if response and getattr(response, "text", None)
            else "No description generated"
        )
    except Exception as e:
        logger.error(f"Error with Gemini: {e}")
        return "Error generating description"

def save_image_with_metadata(image_bytes: bytes, save_path: str, description: str):
    """Save image file with embedded metadata in EXIF format."""
    try:
        img = Image.open(io.BytesIO(image_bytes)).convert("RGB")
        exif_dict = {"0th": {}, "Exif": {}, "GPS": {}, "1st": {}, "thumbnail": None}
        exif_dict["0th"][piexif.ImageIFD.ImageDescription] = description.encode(
            "utf-8", errors="ignore"
        )
        exif_bytes = piexif.dump(exif_dict)
        img.save(save_path, "JPEG", exif=exif_bytes)
    except Exception as e:
        logger.error(f"Error saving image with metadata: {e}")
        # Fallback: save without metadata
        img = Image.open(io.BytesIO(image_bytes)).convert("RGB")
        img.save(save_path, "JPEG")

def extract_images_from_folder(image_folder_path: str, image_output_dir: str):
    """Extract images from folder, generate metadata, and save them with metadata."""
    os.makedirs(image_output_dir, exist_ok=True)
    
    # Get all image files
    image_files = []
    for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']:
        image_files.extend(glob.glob(os.path.join(image_folder_path, f"*{ext}")))
        image_files.extend(glob.glob(os.path.join(image_folder_path, f"*{ext.upper()}")))
    
    prompt = """
    You are an assistant specializing in vehicle and automotive analysis.
    
    Analyze this vehicle image and return a JSON object with:
    {
    "vehicle_type": "<car, truck, motorcycle, etc.>",
    "make_model": "<brand and model if identifiable>", 
    "color": "<primary color>",
    "condition": "<new, used, damaged, etc.>",
    "features": {"body_style": "...", "doors": "...", "special_features": "..."},
    "setting": "<location/context>",
    "image_context": "<detailed description>",
    "tags": ["keyword1", "keyword2", ...]
    }
    
    Rules: Always include all keys. Output only valid JSON.
    """
    
    for image_file_path in image_files:
        try:
            with open(image_file_path, "rb") as f:
                image_bytes = f.read()
            
            original_name = os.path.splitext(os.path.basename(image_file_path))[0]
            unique_filename = f"{original_name}_{uuid.uuid4().hex}.jpg"
            output_path = os.path.join(image_output_dir, unique_filename)
            
            # Generate metadata using Gemini
            description = generate_metadata_with_gemini(image_bytes, prompt)
            
            # Save image with metadata
            save_image_with_metadata(image_bytes, output_path, description)
            
            logger.info(f"✅ Processed: {unique_filename}")
            
            # Add delay to prevent API rate limiting
            time.sleep(10)
            
        except Exception as e:
            logger.error(f"❌ Error processing {image_file_path}: {e}")
            continue

# ============================================================================
# DATA PROCESSING FUNCTIONS
# ============================================================================

def process_pdf_text(pdf_path: str) -> list:
    """Extract text content from PDF document."""
    logger.info("Processing PDF text...")
    doc = fitz.open(pdf_path)
    text_elements = []
    
    for page_num in range(len(doc)):
        page = doc.load_page(page_num)
        text = page.get_text("text")
        if text.strip():
            text_elements.append(text)
    
    doc.close()
    logger.info(f"✅ Extracted text from {len(text_elements)} pages")
    return text_elements

def process_images() -> list:
    """Process images and create image elements."""
    logger.info("Processing images...")
    
    # Extract images if directory is empty
    if len(os.listdir(image_output_dir)) == 0:
        logger.info("Extracting images from folder...")
        extract_images_from_folder(image_dir, image_output_dir)
    
    # Create image elements from processed images
    image_elements = []
    for filename in os.listdir(image_output_dir):
        if filename.lower().endswith(('.jpg', '.jpeg')):
            img_path = os.path.join(image_output_dir, filename)
            metadata_desc = read_image_metadata(img_path)
            
            if metadata_desc:
                # Create summary using OpenAI
                try:
                    ext = img_path.split(".")[-1]
                    pil_image = Image.open(img_path).convert("RGB")
                    buffered = io.BytesIO()
                    pil_image.save(buffered, format="PNG")
                    img_base64 = base64.b64encode(buffered.getvalue()).decode()
                    
                    response = client.chat.completions.create(
                        model="gpt-4o",
                        messages=[{
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": "Create a descriptive summary combining the metadata and visual image. "
                                           "Focus on: vehicle type, make/model, key features, and condition.\n\n"
                                           f"Metadata:\n{json.dumps(metadata_desc, ensure_ascii=False, indent=2)}",
                                },
                                {
                                    "type": "image_url",
                                    "image_url": {"url": f"data:image/{ext};base64,{img_base64}"},
                                },
                            ],
                        }],
                        max_tokens=300,
                    )
                    
                    summary = response.choices[0].message.content
                    element = Element(type="image", text=summary, image_path=img_path)
                    image_elements.append(element)
                    
                except Exception as e:
                    logger.error(f"Error processing {filename}: {e}")
                    continue
    
    logger.info(f"✅ Processed {len(image_elements)} image elements")
    return image_elements

def create_categorized_elements(text_elements: list, image_elements: list) -> list:
    """Create categorized elements from text and image elements."""
    categorized_elements = []

    # Add text elements
    for text in text_elements:
        categorized_elements.append(Element(type="text", text=text))

    # Add image elements
    categorized_elements.extend(image_elements)

    return categorized_elements

def generate_summaries(text_elements: list, image_elements: list) -> tuple:
    """Generate summaries for text and image elements."""
    logger.info("Generating summaries...")

    # Setup summarization chain
    prompt_text = """You are an assistant tasked with summarizing tables, text, and descriptions of images.
    Give a concise summary of the provided content. Content chunk: {element} """
    prompt = ChatPromptTemplate.from_template(prompt_text)

    model = ChatOpenAI(
        temperature=0,
        model="gpt-4o",
        api_key=os.environ.get("OPENAI_API_KEY"),
    )

    summarize_chain = {"element": lambda x: x} | prompt | model | StrOutputParser()

    # Generate text summaries
    text_summaries = summarize_chain.batch(
        [e.text for e in text_elements], {"max_concurrency": 5}
    )

    # Use existing summaries for images
    image_summaries = [e.text for e in image_elements]

    logger.info(f"✅ Generated {len(text_summaries)} text summaries and {len(image_summaries)} image summaries")
    return text_summaries, image_summaries

def setup_rag_system(text_elements: list, image_elements: list, text_summaries: list, image_summaries: list, docstore_data=None, doc_ids=None, image_ids=None):
    """Setup the RAG system with vector store and retriever."""
    logger.info("Setting up RAG system...")

    # Initialize vector store and retriever
    vectorstore = Chroma(
        collection_name="summaries",
        embedding_function=OpenAIEmbeddings(api_key=os.environ.get("OPENAI_API_KEY")),
    )

    store = InMemoryStore()
    if docstore_data:
        store.store = docstore_data

    id_key = "doc_id"

    retriever = MultiVectorRetriever(
        vectorstore=vectorstore,
        docstore=store,
        id_key=id_key,
        k=6,
    )

    # Generate IDs if not provided
    if doc_ids is None:
        doc_ids = [str(uuid.uuid4()) for _ in text_elements]
    if image_ids is None:
        image_ids = [str(uuid.uuid4()) for _ in image_elements]

    # Add text summaries to vector store
    summary_texts_docs = [
        Document(page_content=s, metadata={id_key: doc_ids[i], "type": "text_summary"})
        for i, s in enumerate(text_summaries)
    ]
    retriever.vectorstore.add_documents(summary_texts_docs)

    # Store original text content in document store
    if docstore_data is None:
        retriever.docstore.mset(
            list(zip(
                doc_ids,
                [Document(page_content=e.text, metadata={"type": "original_text"}) for e in text_elements],
            ))
        )

    # Add image summaries to vector store
    summary_images_docs = [
        Document(page_content=s, metadata={id_key: image_ids[i], "type": "image_summary"})
        for i, s in enumerate(image_summaries)
    ]
    retriever.vectorstore.add_documents(summary_images_docs)

    # Store original image paths in document store
    if docstore_data is None:
        retriever.docstore.mset(
            list(zip(
                image_ids,
                [Document(page_content=e.image_path, metadata={"type": "original_image_path"}) for e in image_elements],
            ))
        )

    logger.info("✅ RAG system setup complete")
    return retriever, store, doc_ids, image_ids

def setup_query_chain(retriever):
    """Setup the query processing chain."""
    logger.info("Setting up query chain...")

    def _format_context(docs):
        """Format retrieved documents for presentation to the language model."""
        formatted_docs = []
        for doc in docs:
            if doc.metadata.get("type") == "original_image_path":
                image_name = os.path.basename(doc.page_content) if doc.page_content else "unknown"
                formatted_docs.append(f"Image Reference: {image_name}")
            else:
                formatted_docs.append(doc.page_content)
        return "\n\n".join(formatted_docs)

    template = """Answer the question based only on the following context, which can include text and references to images.
If an image is relevant, mention its filename or a descriptive reference.

Context:
{context}

Question: {question}
"""
    prompt = ChatPromptTemplate.from_template(template)

    model = ChatOpenAI(
        temperature=0,
        model="gpt-4o",
        api_key=os.environ.get("OPENAI_API_KEY")
    )

    chain = (
        {"context": retriever | _format_context, "question": RunnablePassthrough()}
        | prompt
        | model
        | StrOutputParser()
    )

    logger.info("✅ Query chain setup complete")
    return chain

def run_sample_queries(chain):
    """Run sample queries to demonstrate the system."""
    logger.info("Running sample queries...")

    queries = [
        "What is the starting price range for the Porsche 911 Targa 4 (992)?",
        "What is the engine size and horsepower of the 2021 Toyota Corolla E210?",
        "What is the brand and vehicle type of the Mazda MX-5?",
    ]

    for query in queries:
        answer = chain.invoke(query)
        print(f"\nQuery: {query}")
        print(f"Answer: {answer}")
        print("-" * 80)

def main():
    """Main function that orchestrates the entire pipeline with caching."""
    logger.info("🚀 Starting Multimodal RAG System with Caching")

    # Create output directory
    os.makedirs(image_output_dir, exist_ok=True)

    # Check if cache is valid
    if data_manager.is_cache_valid(pdf_path, image_dir):
        logger.info("📦 Loading data from cache...")

        # Load from cache
        cached_data = data_manager.load_data()
        if cached_data:
            (text_elements, image_elements, text_summaries, image_summaries,
             categorized_elements, docstore_data, doc_ids, image_ids) = cached_data

            logger.info(f"✅ Loaded from cache: {len(text_elements)} text elements, {len(image_elements)} image elements")
        else:
            logger.error("❌ Failed to load from cache, will process from scratch")
            cached_data = None
    else:
        logger.info("🔄 Cache invalid or missing, processing from scratch...")
        cached_data = None

    # Process data if not loaded from cache
    if cached_data is None:
        # Process PDF text
        text_elements = process_pdf_text(pdf_path)

        # Process images
        image_elements = process_images()

        # Create categorized elements
        categorized_elements = create_categorized_elements(text_elements, image_elements)

        # Generate summaries
        text_summaries, image_summaries = generate_summaries(text_elements, image_elements)

        # Setup RAG system (this will create docstore)
        retriever, store, doc_ids, image_ids = setup_rag_system(
            text_elements, image_elements, text_summaries, image_summaries
        )

        # Save to cache
        logger.info("💾 Saving processed data to cache...")
        success = data_manager.save_data(
            text_elements=[e.text if hasattr(e, 'text') else e for e in text_elements],
            image_elements=image_elements,
            text_summaries=text_summaries,
            image_summaries=image_summaries,
            categorized_elements=categorized_elements,
            docstore=store,
            doc_ids=doc_ids,
            image_ids=image_ids,
            pdf_path=pdf_path,
            image_dir=image_dir
        )

        if success:
            logger.info("✅ Data saved to cache successfully")
        else:
            logger.warning("⚠️ Failed to save data to cache")

    else:
        # Setup RAG system with cached data
        retriever, store, doc_ids, image_ids = setup_rag_system(
            text_elements, image_elements, text_summaries, image_summaries,
            docstore_data=docstore_data, doc_ids=doc_ids, image_ids=image_ids
        )

    # Setup query chain
    chain = setup_query_chain(retriever)

    # Display statistics
    print(f"\n📊 System Statistics:")
    print(f"Text elements: {len(text_elements)}")
    print(f"Image elements: {len(image_elements)}")
    print(f"Text summaries: {len(text_summaries)}")
    print(f"Image summaries: {len(image_summaries)}")

    # Display cache info
    cache_info = data_manager.get_cache_info()
    if cache_info.get("cache_exists"):
        print(f"Cache size: {cache_info.get('cache_size_mb', 0)} MB")
        print(f"Cache created: {cache_info.get('created_at', 'Unknown')}")

    # Run sample queries
    run_sample_queries(chain)

    logger.info("🎉 Multimodal RAG System completed successfully!")

if __name__ == "__main__":
    main()

%pip install --quiet "langchain_core==0.3.60"


%pip install --quiet transformers

%pip install --quiet pymupdf
%pip install --quiet torch
%pip install --quiet pillow
%pip install --quiet transformers
%pip install --quiet langchain
%pip install --quiet numpy
%pip install --quiet sklearn
   
   

pip show langchain_core

import sys
import subprocess

# ✅ List of dependencies you need
packages = [
    "pymupdf",                # for fitz
    "langchain>=0.2.0",       # main langchain
    "langchain-core>=0.2.0",
    "langchain-community>=0.2.0",
    "transformers",           # HuggingFace
    "pillow",                 # for PIL
    "torch",                  # PyTorch
    "numpy",
    "scikit-learn",
    "faiss-cpu"               # FAISS index (use faiss-gpu if you have CUDA)
]

print(f"🔍 Current Python executable: {sys.executable}")

# ✅ Install all required packages into THIS Python environment
for pkg in packages:
    try:
        print(f"\n📦 Installing {pkg} ...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-U", pkg])
    except Exception as e:
        print(f"❌ Failed to install {pkg}: {e}")

print("\n✅ Installation finished. Now verifying imports...\n")

# ✅ Verification checks
try:
    import fitz
    print("✔ fitz (PyMuPDF) OK")
    from langchain_core.documents import Document
    print("✔ langchain_core OK")
    from langchain_community.vectorstores import FAISS
    print("✔ langchain_community OK")
    from transformers import CLIPProcessor, CLIPModel
    print("✔ transformers OK")
    from PIL import Image
    print("✔ Pillow (PIL) OK")
    import torch
    print("✔ torch OK")
    import numpy as np
    print("✔ numpy OK")
    from sklearn.metrics.pairwise import cosine_similarity
    print("✔ scikit-learn OK")
    print("\n🎉 All libraries imported successfully!")
except Exception as e:
    print("❌ Import check failed:", e)


import sys
!{sys.executable} -m pip install -U "pydantic>=2.7,<3"


from langchain_core.documents import Document
print("✅ langchain_core works")


import fitz  # PyMuPDF
from langchain_core.documents import Document
from transformers import CLIPProcessor, CLIPModel
from PIL import Image
import torch
import numpy as np
from langchain.chat_models import init_chat_model
from langchain.prompts import PromptTemplate
from langchain.schema.messages import HumanMessage
from sklearn.metrics.pairwise import cosine_similarity
import os
import base64
import io
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import FAISS

###Clip Model
import os
from langchain_openai import OpenAIEmbeddings
from dotenv import load_dotenv
load_dotenv()

## set up the environment
os.environ["OPENAI_API_KEY"]=os.getenv("OPENAI_API_KEY")

# Initialize OpenAI embeddings
openai_embeddings = OpenAIEmbeddings(
    api_key=os.getenv("OPENAI_API_KEY"),
    model="text-embedding-3-small"  # or "text-embedding-3-large" for better quality
)

openai_embeddings


# ### initialize the Clip Model for unified embeddings
# clip_model=CLIPModel.from_pretrained("openai/clip-vit-base-patch32")
# clip_processor=CLIPProcessor.from_pretrained("openai/clip-vit-base-patch32")
# clip_model.eval()

import json

### Embedding functions
# def embed_image(image_data):
#     """Embed image using CLIP"""
#     if isinstance(image_data, str):  # If path
#         image = Image.open(image_data).convert("RGB")
#     else:  # If PIL Image
#         image = image_data
    
#     inputs=clip_processor(images=image,return_tensors="pt")
#     with torch.no_grad():
#         features = clip_model.get_image_features(**inputs)
#         # Normalize embeddings to unit vector
#         features = features / features.norm(dim=-1, keepdim=True)
#         return features.squeeze().numpy()
    
# def embed_text(text):
#     """Embed text using CLIP."""
#     inputs = clip_processor(
#         text=text, 
#         return_tensors="pt", 
#         padding=True,
#         truncation=True,
#         max_length=77  # CLIP's max token length
#     )
#     with torch.no_grad():
#         features = clip_model.get_text_features(**inputs)
#         # Normalize embeddings
#         features = features / features.norm(dim=-1, keepdim=True)
#         return features.squeeze().numpy()

# def embed_metadata(metadata: dict):
#     """Convert metadata JSON to string and embed with CLIP text encoder."""
#     # Turn JSON into a flat string
#     metadata_str = json.dumps(metadata, ensure_ascii=False)
#     return embed_text(metadata_str)

### Updated Embedding functions
def embed_text(text):
    """Embed text using OpenAI embeddings."""
    try:
        embedding = openai_embeddings.embed_query(text)
        return np.array(embedding)
    except Exception as e:
        print(f"Error embedding text: {e}")
        return np.zeros(1536)  # Default dimension for text-embedding-3-small

def embed_metadata(metadata: dict):
    """Convert metadata JSON to string and embed with OpenAI."""
    metadata_str = json.dumps(metadata, ensure_ascii=False)
    return embed_text(metadata_str)


# Install piexif and google-generativeai
%pip install piexif google-generativeai
  

# Install piexif and google-generativeai
%pip install google-generativeai
  

#Import gemini 
import piexif
import google.generativeai as genai
import os
import fitz
from langchain_core.documents import Document
from transformers import CLIPProcessor, CLIPModel
from PIL import Image
import torch
import numpy as np
from langchain.chat_models import init_chat_model
from langchain.prompts import PromptTemplate
from langchain.schema.messages import HumanMessage
from sklearn.metrics.pairwise import cosine_similarity
import base64
import io
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import FAISS


# Set up gemini api key


# Configure Gemini API
genai.configure(api_key="AIzaSyA3zurX0KH6ZURcu3cn77zPEZ4b_GEduf8")



prompt = """
You are extracting metadata for a Doraemon character image. 
Your task: Identify which character this image most likely represents, 
and output structured JSON with these keys:
- character_name
- alias (if any)
- role_in_show
- relationships (best_friend, crush, rival, classmates, etc.)
- appearance (clothing, expression, pose)
- image_context (short description of how the image looks)
- tags (keywords)

Be concise but informative. Only include information relevant to Doraemon characters.
"""



def generate_metadata_with_gemini(image_bytes: bytes , prompt: str) -> str:
    """Send image to Gemini to generate metadata/description."""
    model = genai.GenerativeModel("gemini-2.0-flash")
    # img = genai.Image.from_bytes(image_bytes)
    response = model.generate_content([prompt, {"mime_type": "image/jpeg", "data": image_bytes}])
    return response.text.strip() if response and response.text else "No description generated"


def save_image_with_metadata(image_bytes: bytes, save_path: str, description: str):
    """Save image and embed description in EXIF metadata."""
    img = Image.open(io.BytesIO(image_bytes)).convert("RGB")
    exif_dict = {"0th": {}, "Exif": {}, "GPS": {}, "1st": {}, "thumbnail": None}

    # Put metadata into ImageDescription tag
    exif_dict["0th"][piexif.ImageIFD.ImageDescription] = description.encode("utf-8", errors="ignore")

    exif_bytes = piexif.dump(exif_dict)
    img.save(save_path, "JPEG", exif=exif_bytes)


def extract_images_from_pdf(pdf_path: str, output_folder: str = "images"):
    """Extract images from PDF, generate metadata, and save them with metadata."""
    os.makedirs(output_folder, exist_ok=True)
    doc = fitz.open(pdf_path)

    img_count = 0
    for page_num in range(len(doc)):
        page = doc[page_num]
        images = page.get_images(full=True)

        for img_index, img in enumerate(images):
            xref = img[0]
            base_image = doc.extract_image(xref)
            image_bytes = base_image["image"]

            # Generate metadata using Gemini
            description = generate_metadata_with_gemini(image_bytes , prompt)

             # ✅ Display the image in notebook
            img_display = Image.open(io.BytesIO(image_bytes))
            display(img_display)

            # Save image with metadata
            img_filename = f"page{page_num+1}_img{img_index+1}.jpg"
            save_path = os.path.join(output_folder, img_filename)
            save_image_with_metadata(image_bytes, save_path, description)

            print(f"✅ Saved {save_path} with metadata: {description}")
            img_count += 1

    print(f"\n🎉 Done! Extracted and saved {img_count} images into '{output_folder}' folder.")


extract_images_from_pdf("./cartoonData/characters_images.pdf", "images")


## Process PDF
pdf_path="./cartoonData/Doraemon_Character_Descriptions.pdf"
doc=fitz.open(pdf_path)
# Storage for all documents and embeddings
all_docs = []
all_embeddings = []
image_data_store = {}  # Store actual image data for LLM

# Text splitter
splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=100)

doc

for i,page in enumerate(doc):
    ## process text
    text=page.get_text()
    if text.strip():
        ##create temporary document for splitting
        temp_doc = Document(page_content=text, metadata={"page": i, "type": "text"})
        text_chunks = splitter.split_documents([temp_doc])

        #Embed each chunk using CLIP
        for chunk in text_chunks:
            embedding = embed_text(chunk.page_content)
            all_embeddings.append(embedding)
            all_docs.append(chunk)



    ## process images
    ##Three Important Actions:

    ##Convert PDF image to PIL format
    ##Store as base64 for GPT-4V (which needs base64 images)
    ##Create CLIP embedding for retrieval

doc.close()

import glob
from PIL import Image

def read_image_metadata(image_path: str) -> str:
    """Read metadata (description) from image EXIF."""
    img = Image.open(image_path)
    exif_data = img.info.get("exif")
    if not exif_data:
        return None

    try:
        exif_dict = piexif.load(exif_data)
        desc = exif_dict["0th"].get(piexif.ImageIFD.ImageDescription, b"").decode("utf-8", errors="ignore")
        return desc if desc else None
    except Exception:
        return None


# def process_image_folder(folder_path: str):
#     """Process all images in a folder."""

#     for idx, filename in enumerate(os.listdir(folder_path)):
#         if not filename.lower().endswith(".jpg"):
#             continue  # skip non-jpg files

#         try:
#             img_path = os.path.join(folder_path, filename)

#             # Open as PIL
#             pil_image = Image.open(img_path).convert("RGB")

#             # Create unique identifier
#             image_id = f"img_{idx}_{filename}"

#             # Encode as base64
#             buffered = io.BytesIO()
#             pil_image.save(buffered, format="PNG")
#             img_base64 = base64.b64encode(buffered.getvalue()).decode()
#             image_data_store[image_id] = img_base64

#             # Embed
#             img_embedding = embed_image(pil_image)
#             all_embeddings.append(img_embedding)

#             # Extract metadata from EXIF
#             metadata_desc = read_image_metadata(img_path)
#             # metadata_embedding = embed_text(metadata_desc)
#             # all_embeddings.append(metadata_embedding)

#             # Create Document
#             image_doc = Document(
#                 page_content=f"[Image: {image_id} metadata: {metadata_desc}]",
#                 metadata={
#                     "filename": filename,
#                     "image_id": image_id,
#                     "description": metadata_desc,
#                     "type": "image"
#                 }
#             )
#             all_docs.append(image_doc)

#             print(f"✅ Processed {filename} | Metadata: {metadata_desc}")

#         except Exception as e:
#             print(f"❌ Error processing {filename}: {e}")
#             continue

#     return image_data_store, all_embeddings, all_docs

def process_image_folder(folder_path):
    """Process images and embed their metadata using OpenAI embeddings."""
    image_files = glob.glob(os.path.join(folder_path, "*.jpg")) + \
                  glob.glob(os.path.join(folder_path, "*.png"))
    
 
    
    for idx, img_path in enumerate(image_files):
        try:
            filename = os.path.basename(img_path)
            
            # Open as PIL
            pil_image = PIL.open(img_path).convert("RGB")
            
            # Create unique identifier
            image_id = f"img_{idx}_{filename}"
            
            # Encode as base64
            buffered = io.BytesIO()
            pil_image.save(buffered, format="PNG")
            img_base64 = base64.b64encode(buffered.getvalue()).decode()
            image_data_store[image_id] = img_base64
            
            # Extract metadata from EXIF and embed using OpenAI
            metadata_desc = read_image_metadata(img_path)
            metadata_embedding = embed_text(metadata_desc)  # Now uses OpenAI
            all_embeddings.append(metadata_embedding)
            
            # Create Document
            image_doc = Document(
                page_content=f"[Image: {image_id} metadata: {metadata_desc}]",
                metadata={
                    "filename": filename,
                    "image_id": image_id,
                    "description": metadata_desc,
                    "type": "image"
                }
            )
            all_docs.append(image_doc)
            
            print(f"✅ Processed {filename} | Metadata: {metadata_desc}")
            
        except Exception as e:
            print(f"❌ Error processing {filename}: {e}")
            continue
    
    return image_data_store, all_embeddings, all_docs

process_image_folder("images")

all_docs

# Create unified FAISS vector store with CLIP embeddings
embeddings_array = np.array(all_embeddings)
embeddings_array

# Create custom FAISS index since we have precomputed embeddings
vector_store = FAISS.from_embeddings(
    text_embeddings=[(doc.page_content, emb) for doc, emb in zip(all_docs, embeddings_array)],
    embedding=openai_embeddings,  # We're using precomputed embeddings
    metadatas=[doc.metadata for doc in all_docs]
)
vector_store

%pip install --quiet langchain-openai

# Initialize GPT-4 Vision model
llm = init_chat_model("openai:gpt-4.1",api_key="********************************************************************************************************************************************************************",response_format={
        "type": "json_schema",
        "json_schema": {
            "name": "answer_format",
            "schema": {
                "type": "object",
                "properties": {
                    "type": {"type": "string", "enum": ["text", "image"]},
                    "answer": {"type": "string", "description": "The text answer or image URL/base64"}
                },
                "required": ["type", "answer"]
            }
        }
    })
llm

def retrieve_multimodal(query, k=5):
    """Unified retrieval using CLIP embeddings for both text and images."""
    # Embed query using CLIP
    query_embedding = embed_text(query)
    
    # Search in unified vector store
    results = vector_store.similarity_search_by_vector(
      embedding=query_embedding,
      k=k

    )
    
    return results

def create_multimodal_message(query, retrieved_docs):
    """Create a message with both text and images for GPT-4V."""
    content = []
    
    # Add the query
    content.append({
        "type": "text",
        "text": f"Question: {query}\n\nContext:\n"
    })
    
    # Separate text and image documents
    text_docs = [doc for doc in retrieved_docs if doc.metadata.get("type") == "text"]
    image_docs = [doc for doc in retrieved_docs if doc.metadata.get("type") == "image"]

    print("text_docs==>",text_docs)
    print("image_docs==>",image_docs)
    
    # Add text context
    if text_docs:
        text_context = "\n\n".join([
            f"[Page {doc.metadata['page']}]: {doc.page_content}"
            for doc in text_docs
        ])
        content.append({
            "type": "text",
            "text": f"Text excerpts:\n{text_context}\n"
        })
    
    # Add images
    for doc in image_docs:
        print("doc==>",doc)
        image_id = doc.metadata.get("image_id")
        if image_id and image_id in image_data_store:
            content.append({
                "type": "text",
                "text": json.dumps(doc.metadata)
            })
            content.append({
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{image_data_store[image_id]}"
                }
            })
    
    # Add instruction
    content.append({
        "type": "text",
        "text": """
    Please answer the question based on the provided text and images.

    ⚠️ IMPORTANT:
    - If the answer is text → return plain text.
    - If the answer is an image → return the base64 too
    """
})

    
    return HumanMessage(content=content)

def multimodal_pdf_rag_pipeline(query):
    """Main pipeline for multimodal RAG."""
    # Retrieve relevant documents
    context_docs = retrieve_multimodal(query, k=5)

    
    # Create multimodal message
    message = create_multimodal_message(query, context_docs)
    
    # Get response from GPT-4V
    response = llm.invoke([message])
    
    # Print retrieved context info
    print(f"\nRetrieved {len(context_docs)} documents:")
    for doc in context_docs:
        
        doc_type = doc.metadata.get("type", "unknown")
        page = doc.metadata.get("page", "?")
        if doc_type == "text":
            preview = doc.page_content[:100] + "..." if len(doc.page_content) > 100 else doc.page_content
            print(f"  - Text from page {page}: {preview}")
        else:
            print(f"  - Image from page {page}")
    print("\n")
    
    return response.content

if __name__ == "__main__":
    # Example queries
    queries = [
        "who all wear glasses?"
    ]
    
    for query in queries:
        print(f"\nQuery: {query}")
        print("-" * 50)
        answer = multimodal_pdf_rag_pipeline(query)
        print(f"Answer: {answer}")
        print("=" * 70)
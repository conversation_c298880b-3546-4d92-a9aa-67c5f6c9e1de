# main.py
# =============================================================================
# MULTIMODAL RAG SYSTEM (Robust + Optimized)
# =============================================================================

from __future__ import annotations

import os, io, re, json, uuid, base64, glob, time, shutil
from dataclasses import dataclass
from typing import Any, List, Tuple, Dict, Optional

from dotenv import load_dotenv
load_dotenv()

import fitz  # PyMuPDF
from PIL import Image
import piexif

# LangChain core
from langchain_core.documents import Document
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough, RunnableLambda


# LLM + embeddings + vector DB
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain_chroma import Chroma

# Doc stores / retrievers
from langchain.storage import InMemoryStore
from langchain.retrievers.multi_vector import MultiVectorRetriever

# Optional hybrid (BM25); if not available we’ll fallback to a keyword retriever
try:
    from langchain.retrievers import BM25Retriever, EnsembleRetriever
    HAS_BM25 = True
except Exception:
    HAS_BM25 = False

# ---------------------------
# CONFIG & PATHS
# ---------------------------
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
PDF_PATH = "./carData/vehicle_descriptions_expanded.pdf"
IMAGE_DIR = os.path.join(BASE_DIR, "extracted_images_with_metadata_small")
RAW_IMAGE_INPUT = os.path.join(BASE_DIR, "..", "carData", "carImg")

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise RuntimeError("Missing OPENAI_API_KEY. Put it in your environment or .env")

os.makedirs(IMAGE_DIR, exist_ok=True)

# ---------------------------
# LIGHTWEIGHT HELPERS
# ---------------------------

@dataclass
class Element:
    type: str            # "text" or "image"
    text: Any
    image_path: Optional[str] = None
    brand: Optional[str] = None
    model: Optional[str] = None


def extract_field(text: str, field_name: str) -> str:
    """
    Extracts a value from lines like 'Brand/Manufacturer: Toyota'
    and is resilient to extra spaces/casing.
    """
    pattern = rf"^{re.escape(field_name)}\s*:\s*(.+)$"
    for line in text.splitlines():
        m = re.match(pattern, line.strip(), flags=re.IGNORECASE)
        if m:
            return m.group(1).strip()
    return "Unknown"


def parse_brand_model_from_text_page(page_text: str) -> Tuple[str, str]:
    # Customize these field names to exactly match your PDF
    brand = extract_field(page_text, "Brand/Manufacturer")
    model = extract_field(page_text, "Name/Model")
    # Fallback: try looser heuristics if still Unknown
    if brand == "Unknown":
        # Search “Brand” alone
        b2 = extract_field(page_text, "Brand")
        if b2 != "Unknown":
            brand = b2
    if model == "Unknown":
        # Try “Model”
        m2 = extract_field(page_text, "Model")
        if m2 != "Unknown":
            model = m2
    return brand, model


def read_image_exif_description(image_path: str) -> Optional[str]:
    img = Image.open(image_path)
    exif = img.info.get("exif")
    if not exif:
        return None
    try:
        exif_dict = piexif.load(exif)
        desc = exif_dict["0th"].get(piexif.ImageIFD.ImageDescription, b"")
        return desc.decode("utf-8", errors="ignore") if desc else None
    except Exception:
        return None


def guess_brand_model_from_filename(filename: str) -> Tuple[str, str]:
    """
    Very light heuristic: try to split by '_' and use first token as model.
    """
    base = os.path.basename(filename)
    stem = os.path.splitext(base)[0]
    toks = re.split(r"[_\-\s]+", stem)
    model = toks[0] if toks else "Unknown"
    # No brand in filename → leave Unknown. You can extend this mapping if needed.
    return "Unknown", model


def safe_json_pretty(s: Optional[str]) -> str:
    if not s:
        return ""
    # If EXIF stored valid JSON, pretty-print; else return as-is
    try:
        return json.dumps(json.loads(s), ensure_ascii=False, indent=2)
    except Exception:
        return s


# ---------------------------
# LOAD & PREPARE CONTENT
# ---------------------------

print("🗂️  Reading PDF:", PDF_PATH)
doc = fitz.open(PDF_PATH)

text_pages: List[str] = []
for p in range(len(doc)):
    page = doc.load_page(p)
    t = page.get_text("text")
    if t and t.strip():
        text_pages.append(t)

print(f"✅ Extracted text pages: {len(text_pages)}")

# Build Element list from text pages (keep full structured text!)
elements: List[Element] = []
for page_text in text_pages:
    brand, model = parse_brand_model_from_text_page(page_text)
    # Force inclusion of a short, consistent “summary header” to help retrieval
    summary_header = (
        f"Vehicle Summary\n"
        f"- Brand/Company: {brand}\n"
        f"- Model: {model}\n"
        f"- Contains Technical Specifications, Features, Use Cases, Market Segment."
    )
    elements.append(
        Element(
            type="text",
            text=f"{summary_header}\n\n{page_text}",
            brand=brand,
            model=model
        )
    )

# Bring in any already-processed images (with EXIF → ImageDescription)
if os.path.isdir(IMAGE_DIR):
    for filename in os.listdir(IMAGE_DIR):
        if filename.lower().endswith((".jpg", ".jpeg", ".png", ".webp")):
            img_path = os.path.join(IMAGE_DIR, filename)
            meta_text = read_image_exif_description(img_path)
            brand_i, model_i = guess_brand_model_from_filename(filename)
            elements.append(
                Element(
                    type="image",
                    text=meta_text or f"Image metadata unavailable. File: {filename}",
                    image_path=img_path,
                    brand=brand_i,
                    model=model_i
                )
            )

print(f"🧮 Elements → text: {sum(1 for e in elements if e.type=='text')}, "
      f"image: {sum(1 for e in elements if e.type=='image')}")

# ---------------------------
# BUILD VECTORSTORE + DOCSTORE
# ---------------------------

embeddings = OpenAIEmbeddings(api_key=OPENAI_API_KEY)

vectorstore = Chroma(
    collection_name="summaries",
    embedding_function=embeddings
)

docstore = InMemoryStore()
id_key = "doc_id"

# MultiVectorRetriever will:
# 1) store many tiny vectors (one per summary) in vectorstore
# 2) on retrieval, collapse to parent docs from docstore by id_key
multi_vector = MultiVectorRetriever(
    vectorstore=vectorstore,
    docstore=docstore,
    id_key=id_key,
    k=20,
)

# Insert text documents (as summaries) into vectorstore, with original full text into docstore
text_docs: List[Document] = []
text_ids: List[str] = []

for e in elements:
    if e.type != "text":
        continue
    doc_id = str(uuid.uuid4())
    text_ids.append(doc_id)

    # Summary is already prepended in e.text; keep both “summary header” and full page text
    # Store brand/model in metadata for future filtering
    text_docs.append(
        Document(
            page_content=e.text,
            metadata={id_key: doc_id, "type": "text_summary",
                      "brand": e.brand or "Unknown", "model": e.model or "Unknown"}
        )
    )
    # Original content = the same (we want the full text retrievable)
    docstore.mset([(doc_id, Document(page_content=e.text, metadata={"type": "original_text"}))])

# Add text vectors
if text_docs:
    vectorstore.add_documents(text_docs)

# Insert image documents (summaries / EXIF description) into vectorstore and store original path
image_docs: List[Document] = []
image_ids: List[str] = []

for e in elements:
    if e.type != "image":
        continue
    doc_id = str(uuid.uuid4())
    image_ids.append(doc_id)

    image_docs.append(
        Document(
            page_content=(safe_json_pretty(e.text) if e.text else f"Image file: {os.path.basename(e.image_path or '')}"),
            metadata={id_key: doc_id, "type": "image_summary",
                      "brand": e.brand or "Unknown", "model": e.model or "Unknown"}
        )
    )
    # Store original image path so we can show filename in the final context
    if e.image_path:
        docstore.mset([(doc_id, Document(page_content=e.image_path, metadata={"type": "original_image_path"}))])

if image_docs:
    vectorstore.add_documents(image_docs)

print("📦 Vectorstore count:", vectorstore._collection.count())

# ---------------------------
# HYBRID RETRIEVAL
# ---------------------------

# Build a BM25 retriever for text docs (if available). Fallback to simple keyword scorer.
def build_bm25_or_keyword(elements: List[Element]):
    text_only_docs = [Document(page_content=e.text) for e in elements if e.type == "text"]
    if HAS_BM25 and text_only_docs:
        bm25 = BM25Retriever.from_documents(text_only_docs)
        bm25.k = 20
        return bm25
    else:
        # Very simple keyword fallback
        class SimpleKeywordRetriever:
            def __init__(self, docs: List[Document], k: int = 20):
                self.docs = docs
                self.k = k

            def get_relevant_documents(self, query: str) -> List[Document]:
                q_tokens = set(re.findall(r"\w+", query.lower()))
                scored = []
                for d in self.docs:
                    text = (d.page_content or "").lower()
                    tokens = set(re.findall(r"\w+", text))
                    score = len(q_tokens & tokens)
                    if score > 0:
                        scored.append((score, d))
                scored.sort(key=lambda x: x[0], reverse=True)
                return [d for _, d in scored[:self.k]]

            # LangChain compat
            def invoke(self, query: str):
                return self.get_relevant_documents(query)

        return SimpleKeywordRetriever(text_only_docs, k=20)

bm25_or_kw = build_bm25_or_keyword(elements)

if HAS_BM25:
    retriever = EnsembleRetriever(
        retrievers=[multi_vector, bm25_or_kw],
        weights=[0.7, 0.3]
    )
else:
    # Manual ensemble (if BM25 not available): union and simple rerank by keyword overlaps
    def hybrid_retrieve(query: str, k: int = 15) -> List[Document]:
        vec_docs = multi_vector.invoke(query)
        kw_docs = bm25_or_kw.invoke(query) if bm25_or_kw else []
        combined = vec_docs + kw_docs
        # Deduplicate by page_content+metadata tuple
        seen = set()
        unique_docs = []
        for d in combined:
            key = (d.page_content, tuple(sorted((d.metadata or {}).items())))
            if key not in seen:
                seen.add(key)
                unique_docs.append(d)
        # Simple keyword boost
        q_tokens = set(re.findall(r"\w+", query.lower()))
        def kw_score(doc: Document) -> int:
            t = (doc.page_content or "").lower()
            toks = set(re.findall(r"\w+", t))
            return len(q_tokens & toks)
        unique_docs.sort(key=lambda d: kw_score(d), reverse=True)
        # Return top k
        return unique_docs[:k]

    class HybridRetrieverWrapper:
        def invoke(self, query: str):
            return hybrid_retrieve(query)

    retriever = HybridRetrieverWrapper()

# ---------------------------
# LLM & PROMPT
# ---------------------------

qa_prompt = ChatPromptTemplate.from_template(
    """Answer strictly from the provided context (text + image references). 
If an image is relevant, include its filename in your answer.

Context:
{context}

Question: {question}"""
)

llm = ChatOpenAI(
    temperature=0,
    model="gpt-4o",
    api_key=OPENAI_API_KEY,
)

def _format_context(docs: List[Document]) -> str:
    formatted = []
    for d in docs:
        mtype = (d.metadata or {}).get("type")
        if mtype == "original_image_path":
            formatted.append(f"[Image: {os.path.basename(d.page_content)}]")
        else:
            formatted.append(d.page_content)
    return "\n\n".join(formatted)

chain = (
    {
        "context": RunnableLambda(lambda q: retriever.invoke(q)) | RunnableLambda(_format_context),
        "question": RunnablePassthrough(),
    }
    | qa_prompt
    | llm
    | StrOutputParser()
)


# ---------------------------
# DEMO QUERIES
# ---------------------------
if __name__ == "__main__":
    queries = [
        "how many companies are there in the pdf?",
        "tell me Technical Specifications about Maruti Suzuki Swift and give image too",
        "compare Toyota and Honda on fuel efficiency",
        "show me images of Hyundai cars",
        "what is the market segment and price range for the Corolla?"
    ]
    for q in queries:
        print("\n" + "-" * 80)
        print("Query:", q)
        try:
            ans = chain.invoke(q)
            print("Answer:", ans)
        except Exception as e:
            print("Error:", e)
    print("\n📦 Final vector count:", vectorstore._collection.count())

import logging
from fastapi import <PERSON>AP<PERSON>, Request
from fastapi.responses import JSONResponse
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


# Initialize FastAPI app and services
app = FastAPI()


@app.get("/")
async def health_check():
    print("main :: health_check :: status")
    return {"status": "health check okay"}


@app.exception_handler(Exception)
async def universal_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unexpected error: {str(exc)}")
    return JSONResponse(status_code=500, content={"message": "Internal Server Error", "detail": str(exc)})

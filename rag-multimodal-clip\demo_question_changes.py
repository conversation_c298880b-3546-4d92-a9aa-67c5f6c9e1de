#!/usr/bin/env python3
# ============================================================================
# DEMONSTRATION: CHANGING QUESTIONS WITH CACHING
# ============================================================================
"""
This script demonstrates what happens when you change questions
while using the caching system.
"""

import time
import os
from data_manager import DataManager

def demonstrate_question_changes():
    """Show what happens when questions change with caching enabled."""
    
    print("🎬 Demo: Changing Questions with Caching")
    print("=" * 60)
    
    # Initialize data manager
    dm = DataManager()
    
    print("\n🧠 Understanding the Cache Logic:")
    print("   📦 CACHED (Expensive): PDF processing, image analysis, embeddings")
    print("   🔄 NOT CACHED (Always fresh): Questions and answers")
    
    # Show current cache status
    cache_info = dm.get_cache_info()
    
    if cache_info.get("cache_exists"):
        print(f"\n✅ Cache Status: Active")
        print(f"   📊 Size: {cache_info.get('cache_size_mb', 0)} MB")
        print(f"   📝 Text elements: {cache_info.get('text_elements_count', 0)}")
        print(f"   🖼️  Image elements: {cache_info.get('image_elements_count', 0)}")
    else:
        print(f"\n❌ No cache found - first run will be slow")
    
    print(f"\n🎯 What Happens When You Change Questions:")
    
    print(f"\n1️⃣ ORIGINAL QUESTIONS (in main_with_cache.py):")
    original_questions = [
        "What is the starting price range for the Porsche 911 Targa 4 (992)?",
        "What is the engine size and horsepower of the 2021 Toyota Corolla E210?",
        "What is the brand and vehicle type of the Mazda MX-5?",
    ]
    
    for i, q in enumerate(original_questions, 1):
        print(f"   Q{i}: {q}")
    
    print(f"\n2️⃣ NEW QUESTIONS (examples you might want to ask):")
    new_questions = [
        "What are the most expensive vehicles in the dataset?",
        "Which cars have the best fuel efficiency?",
        "Show me all electric or hybrid vehicles.",
        "What vehicles are suitable for families?",
        "Which cars have manual transmission?",
        "What are the newest model years available?",
        "Show me luxury sports cars.",
        "What vehicles have the highest horsepower?",
    ]
    
    for i, q in enumerate(new_questions, 1):
        print(f"   Q{i}: {q}")
    
    print(f"\n⚡ Performance Impact of Changing Questions:")
    print(f"   🔄 Cache validation: ~1 second")
    print(f"   📦 Cache loading: ~5 seconds") 
    print(f"   🏗️  RAG setup: ~3 seconds")
    print(f"   ❓ New question processing: ~2-5 seconds per question")
    print(f"   📊 TOTAL TIME: ~10-15 seconds (regardless of questions!)")
    
    print(f"\n🔍 What Gets Checked for Cache Validity:")
    print(f"   ✅ PDF file MD5 hash (detects file changes)")
    print(f"   ✅ Image directory file count (detects added/removed images)")
    print(f"   ✅ Cache file completeness (all required files exist)")
    print(f"   ❌ Questions are NOT checked (they don't affect cache)")
    
    print(f"\n📋 Step-by-Step Process When You Change Questions:")
    
    steps = [
        ("🔍 Check cache validity", "~1 sec", "Based on source files, not questions"),
        ("📦 Load cached data", "~5 sec", "PDF text, image metadata, summaries"),
        ("🏗️  Setup RAG system", "~3 sec", "Vector store, retriever, query chain"),
        ("❓ Process new questions", "~2-5 sec each", "Generate answers using cached data"),
        ("📄 Display results", "instant", "Show answers to your new questions")
    ]
    
    for i, (step, time_est, description) in enumerate(steps, 1):
        print(f"   {i}. {step:<25} {time_est:<10} - {description}")
    
    print(f"\n🎮 Try This Yourself:")
    print(f"   1. Run: python main_with_cache.py")
    print(f"   2. Edit the 'queries' list in main_with_cache.py (line ~489)")
    print(f"   3. Add your own questions like:")
    print(f"      - 'What are the cheapest cars available?'")
    print(f"      - 'Show me all SUVs in the dataset.'")
    print(f"      - 'Which vehicles have the best safety ratings?'")
    print(f"   4. Run: python main_with_cache.py (will be fast!)")
    
    print(f"\n💡 Pro Tips:")
    print(f"   • Questions can be completely different - cache still works")
    print(f"   • You can ask as many questions as you want")
    print(f"   • Only source data changes invalidate cache")
    print(f"   • Interactive mode: modify questions and re-run instantly")
    
    print(f"\n🚫 Cache Gets Invalidated Only When:")
    print(f"   • PDF file is modified/replaced")
    print(f"   • Images are added/removed from carImg/ folder")
    print(f"   • Cache files are deleted/corrupted")
    print(f"   • You run: python cache_manager.py clear")
    
    # Interactive demonstration
    print(f"\n🎪 Interactive Demo:")
    
    while True:
        print(f"\nChoose an option:")
        print(f"   1. Show example question modifications")
        print(f"   2. Show cache validation details")
        print(f"   3. Exit demo")
        
        try:
            choice = input(f"\nEnter choice (1-3): ").strip()
            
            if choice == "1":
                print(f"\n📝 Example Question Modifications:")
                
                modifications = [
                    {
                        "category": "Price-focused questions",
                        "examples": [
                            "What are the most affordable vehicles?",
                            "Show me luxury cars over $100,000.",
                            "What's the price range for electric vehicles?"
                        ]
                    },
                    {
                        "category": "Performance questions", 
                        "examples": [
                            "Which cars have the highest horsepower?",
                            "Show me vehicles with best acceleration.",
                            "What are the top speed capabilities?"
                        ]
                    },
                    {
                        "category": "Feature-based questions",
                        "examples": [
                            "Which vehicles have all-wheel drive?",
                            "Show me cars with hybrid technology.",
                            "What vehicles have the best fuel economy?"
                        ]
                    }
                ]
                
                for mod in modifications:
                    print(f"\n   🏷️  {mod['category']}:")
                    for example in mod['examples']:
                        print(f"      • {example}")
                
                print(f"\n   ⚡ All of these will run at the same speed (~10 seconds)")
                print(f"   📦 Because they use the same cached processed data")
            
            elif choice == "2":
                print(f"\n🔍 Cache Validation Details:")
                
                pdf_path = "./carData/vehicle_descriptions_expanded.pdf"
                image_dir = "./carData/carImg"
                
                if os.path.exists(pdf_path):
                    print(f"   ✅ PDF exists: {pdf_path}")
                else:
                    print(f"   ❌ PDF missing: {pdf_path}")
                
                if os.path.exists(image_dir):
                    image_count = len([f for f in os.listdir(image_dir) 
                                     if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
                    print(f"   ✅ Image dir exists: {image_dir} ({image_count} images)")
                else:
                    print(f"   ❌ Image dir missing: {image_dir}")
                
                is_valid = dm.is_cache_valid(pdf_path, image_dir)
                print(f"   🔄 Cache valid: {'✅ Yes' if is_valid else '❌ No'}")
                
                if is_valid:
                    print(f"   💡 Changing questions will NOT affect this validation")
                    print(f"   ⚡ Your new questions will run fast!")
                else:
                    print(f"   💡 Cache needs rebuilding (first run will be slow)")
            
            elif choice == "3":
                print(f"\n👋 Demo completed!")
                break
            
            else:
                print(f"   ❌ Invalid choice. Please enter 1-3.")
                
        except KeyboardInterrupt:
            print(f"\n\n👋 Demo interrupted.")
            break
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n🎉 Key Takeaway:")
    print(f"   Questions are processed in REAL-TIME using CACHED data")
    print(f"   Change questions freely - performance stays fast! ⚡")

def main():
    """Run the question change demonstration."""
    try:
        demonstrate_question_changes()
    except KeyboardInterrupt:
        print(f"\n\n👋 Demo interrupted. Goodbye!")
    except Exception as e:
        print(f"\n💥 Demo error: {e}")

if __name__ == "__main__":
    main()

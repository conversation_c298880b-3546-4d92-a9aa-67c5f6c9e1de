#!/usr/bin/env python3
# ============================================================================
# CACHE MANAGEMENT UTILITY
# ============================================================================
"""
Utility script for managing the RAG system cache.
Provides commands to view, clear, and validate cache data.
"""

import argparse
import sys
from pathlib import Path
from data_manager import DataManager
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def show_cache_info(data_manager: DataManager):
    """Display detailed cache information."""
    print("\n" + "="*60)
    print("CACHE INFORMATION")
    print("="*60)
    
    cache_info = data_manager.get_cache_info()
    
    if not cache_info.get("cache_exists"):
        print("❌ No cache found")
        if "error" in cache_info:
            print(f"Error: {cache_info['error']}")
        return
    
    print(f"✅ Cache Status: Active")
    print(f"📁 Cache Directory: {data_manager.cache_dir}")
    print(f"📊 Cache Size: {cache_info.get('cache_size_mb', 0)} MB")
    print(f"📅 Created: {cache_info.get('created_at', 'Unknown')}")
    print(f"📄 PDF Path: {cache_info.get('pdf_path', 'Unknown')}")
    print(f"🖼️  Image Directory: {cache_info.get('image_dir', 'Unknown')}")
    print(f"📝 Text Elements: {cache_info.get('text_elements_count', 0)}")
    print(f"🖼️  Image Elements: {cache_info.get('image_elements_count', 0)}")
    print(f"🔖 Version: {cache_info.get('version', 'Unknown')}")
    
    # Show cache files
    print(f"\n📂 Cache Files:")
    cache_files = [
        ("text_elements.json", "Text content from PDF"),
        ("image_elements.json", "Image elements with metadata"),
        ("text_summaries.json", "Generated text summaries"),
        ("image_summaries.json", "Generated image summaries"),
        ("categorized_elements.json", "All categorized elements"),
        ("docstore.pkl", "LangChain document store"),
        ("doc_ids.json", "Document IDs for text elements"),
        ("image_ids.json", "Document IDs for image elements"),
        ("metadata.json", "Cache metadata and validation info")
    ]
    
    for filename, description in cache_files:
        file_path = data_manager.cache_dir / filename
        if file_path.exists():
            size_kb = file_path.stat().st_size / 1024
            print(f"   ✅ {filename:<25} ({size_kb:.1f} KB) - {description}")
        else:
            print(f"   ❌ {filename:<25} (missing) - {description}")


def validate_cache(data_manager: DataManager, pdf_path: str, image_dir: str):
    """Validate cache against current source files."""
    print("\n" + "="*60)
    print("CACHE VALIDATION")
    print("="*60)
    
    if not Path(pdf_path).exists():
        print(f"❌ PDF file not found: {pdf_path}")
        return False
    
    if not Path(image_dir).exists():
        print(f"❌ Image directory not found: {image_dir}")
        return False
    
    is_valid = data_manager.is_cache_valid(pdf_path, image_dir)
    
    if is_valid:
        print("✅ Cache is valid and up-to-date")
        print("   - PDF file hasn't changed")
        print("   - Image directory hasn't changed")
        print("   - All required cache files exist")
    else:
        print("❌ Cache is invalid or outdated")
        print("   Possible reasons:")
        print("   - PDF file has been modified")
        print("   - Images have been added/removed")
        print("   - Cache files are missing or corrupted")
        print("   - This is the first run (no cache exists)")
    
    return is_valid


def clear_cache(data_manager: DataManager, confirm: bool = False):
    """Clear all cache data."""
    print("\n" + "="*60)
    print("CLEAR CACHE")
    print("="*60)
    
    cache_info = data_manager.get_cache_info()
    if not cache_info.get("cache_exists"):
        print("ℹ️  No cache to clear")
        return
    
    if not confirm:
        print("⚠️  This will delete all cached data!")
        print(f"   Cache size: {cache_info.get('cache_size_mb', 0)} MB")
        print(f"   Text elements: {cache_info.get('text_elements_count', 0)}")
        print(f"   Image elements: {cache_info.get('image_elements_count', 0)}")
        
        response = input("\nAre you sure you want to clear the cache? (y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("❌ Cache clearing cancelled")
            return
    
    success = data_manager.clear_cache()
    if success:
        print("✅ Cache cleared successfully")
        print("   Next run will process all data from scratch")
    else:
        print("❌ Failed to clear cache")


def main():
    """Main function for cache management CLI."""
    parser = argparse.ArgumentParser(
        description="Manage RAG system cache",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python cache_manager.py info                    # Show cache information
  python cache_manager.py validate               # Validate cache
  python cache_manager.py clear                  # Clear cache (with confirmation)
  python cache_manager.py clear --force          # Clear cache without confirmation
  python cache_manager.py validate --pdf ./carData/vehicle_descriptions_expanded.pdf --images ./carData/carImg
        """
    )
    
    parser.add_argument(
        "command",
        choices=["info", "validate", "clear"],
        help="Command to execute"
    )
    
    parser.add_argument(
        "--cache-dir",
        default="data_cache",
        help="Cache directory path (default: data_cache)"
    )
    
    parser.add_argument(
        "--pdf",
        default="./carData/vehicle_descriptions_expanded.pdf",
        help="PDF file path for validation"
    )
    
    parser.add_argument(
        "--images",
        default="./carData/carImg",
        help="Image directory path for validation"
    )
    
    parser.add_argument(
        "--force",
        action="store_true",
        help="Force operation without confirmation"
    )
    
    args = parser.parse_args()
    
    # Initialize data manager
    data_manager = DataManager(cache_dir=args.cache_dir)
    
    try:
        if args.command == "info":
            show_cache_info(data_manager)
        
        elif args.command == "validate":
            validate_cache(data_manager, args.pdf, args.images)
        
        elif args.command == "clear":
            clear_cache(data_manager, confirm=args.force)
        
    except KeyboardInterrupt:
        print("\n❌ Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
